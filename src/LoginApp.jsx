import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Typography,
  Form,
  ConfigProvider,
  theme,
  Space,
  Alert,
  Select
} from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import axios from 'axios';
import jwt_decode from 'jwt-decode';
import {
  setIsLoggedIn,
  setUser,
  setPlant
} from './redux/authSlice';
import GoogleSignInButton from './CustomComponents/GoogleSignInButton';
import logoApp from './images/VisualAIM_icon.png';
import slackIcon from './images/slackicon.svg';
import './Styles/LoginPage.css';

const { Title, Text } = Typography;
const { Option, OptGroup } = Select;

const PLANTS = [
  { display: "1 - TOLEDO", value: "TOLEDO", project: "GP" },
  { display: "2 - BRUNSWUICK", value: "BRUNSWUIC<PERSON>", project: "GP" },
  { display: "3 - MONTICELL<PERSON>", value: "MONTICELLO", project: "GP" },
  { display: "4 - BIG ISLAND", value: "BIG ISLAND", project: "GP" },
  { display: "5 - ALABAMA RIVER", value: "ALABAMA RIVER", project: "GP" },
  { display: "6 - WAUNA", value: "WAUNA", project: "GP" },
  { display: "7 - NAHEOLA", value: "NAHEOLA", project: "GP" },
  { display: "8 - BREWTON", value: "BREWTON", project: "GP" },
  { display: "8.2 - BREWTON_2", value: "BREWTON_2", project: "GP" },
  { display: "9 - LEAF RIVER", value: "LEAF RIVER", project: "GP" },
  { display: "10 - CROSSETT", value: "CROSSETT", project: "GP" },
  { display: "11 - MEMPHIS", value: "MEMPHIS", project: "GP" },
  { display: "12 - CAMAS", value: "CAMAS", project: "GP" },
  { display: "13 - GREEN BAY", value: "GREEN BAY", project: "GP" },
  { display: "14 - SAVANNAH RIVER", value: "SAVANNAH RIVER", project: "GP" },
  { display: "15 - PLATTSBURGH", value: "PLATTSBURGH", project: "GP" },
  { display: "16 - HATTIESBURG", value: "HATTIESBURG", project: "GP" },
  { display: "17 - HALSEY TISSUE", value: "HALSEY TISSUE", project: "GP" },
  { display: "18 - PORT HUDSON", value: "PORT HUDSON", project: "GP" },
  { display: "19 - MUSKOGEE", value: "MUSKOGEE", project: "GP" },
  { display: "20 - DIXIE", value: "DIXIE", project: "GP" },
  { display: "OCI", value: "OCI", project: "OCI" },
  { display: "ECOLAB", value: "ECOLAB", project: "ECOLAB" },
  { display: "MAVERICK TERMINAL LIVE OAK", value: "MAVERICK TERMINAL LIVE OAK", project: "HE" },
  { display: "TEXAS INT. TERMINALS", value: "TEXAS INTERNATIONAL TERMINALS", project: "TIT" },
  { display: "DEV", value: "DEV", project: "DEVELOPMENT" },
  { display: "JAVELINA SMR", value: "JAVELINA SMR", project: "HE" },
  { display: "RIVERTON", value: "RIVERTON", project: "CHEMTRADE" },
  { display: "CAIRO", value: "CAIRO", project: "CHEMTRADE" },
  {display:"TULSA", value:"TULSA", project:"CHEMTRADE"},
  {display:"SYRACUSE", value:"SYRACUSE", project:"CHEMTRADE"},
  {display: "WALLINGFORD", value: "WALLINGFORD", project: "ALLNEX"}

   
];

// Group plants by project
const getPlantsByProject = () => {
  const projectGroups = {};
  
  PLANTS.forEach(plant => {
    if (!projectGroups[plant.project]) {
      projectGroups[plant.project] = [];
    }
    projectGroups[plant.project].push(plant);
  });
  
  return projectGroups;
};

const DEPLOYMENT_DATE = "June 19, 2025";
const SLACK_LINK = "https://visualaimnet.slack.com/archives/D07KCEPP5SQ";



const PlantSelector = ({ value, onChange }) => {
  const projectGroups = getPlantsByProject();
  
  // Sort projects to place CHEMTRADE at the top, then GP, then others alphabetically
  const sortedProjects = Object.keys(projectGroups).sort((a, b) => {
    if( a === "ALLNEX") return -2;
    if (a === "CHEMTRADE") return -1;
    if (b === "CHEMTRADE") return 1;
    if (a === "GP") return -1;
    if (b === "GP") return 1;
    return a.localeCompare(b);
  });
  
  return (
    <Select
      listHeight={350}
      showSearch
      placeholder="🏭 Select a plant"
      optionFilterProp="children"
      onChange={(val) => onChange(val.replace(/^\d+ - /, ''))}
      filterOption={(input, option) => {
        const plant = PLANTS.find(p => p.value === option?.value);
        return plant?.display.toLowerCase().includes(input.toLowerCase());
      }}
      value={value}
      suffixIcon={<GlobalOutlined />}
    >
      {sortedProjects.map(project => (
        <OptGroup key={project} label={project}>
          {projectGroups[project].map((plant) => (
            <Option key={plant.value} value={plant.value}>
              {plant.display}
            </Option>
          ))}
        </OptGroup>
      ))}
    </Select>
  );
};

const LoginApp = () => {
  const [isLoginLoading, setIsLoginLoading] = useState(false);
  const [validationError, setValidationError] = useState("");
  const plant = useSelector((state) => state.auth.plant);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleLogin = async (token) => {
    try {
      setIsLoginLoading(true);
      setValidationError("");

      const response = await axios.post(
        `${process.env.REACT_APP_DATA_API}/validate_token`,
        { token, plant },
        {
          headers: {
            "Content-Type": "application/json",
          },
          withCredentials: true,
        }
      );

      if (response.data.isValid) {
        const userObject = jwt_decode(token);
  
        dispatch(setIsLoggedIn(true));
        dispatch(setUser(userObject));
        localStorage.setItem("user_data", JSON.stringify(userObject));
  
        localStorage.setItem("session_id", response.data.session_id);
        navigate("/");
      } else {
        throw new Error("Token validation failed");
      }
    } catch (error) {
      console.error("Login error:", error);
      if (error.response && error.response.data && error.response.data.detail) {
        setValidationError(error.response.data.detail);
      } else {
        setValidationError("Login failed. Please try again.");
      }
    } finally {
      setIsLoginLoading(false);
    }
  };

  const handleCredentialResponse = async (response) => {
    if (plant) {
      await handleLogin(response.credential);
    } else {
      setValidationError("Please select a plant before logging in.");
    }
  };

  const handlePlantChange = (value) => {
    dispatch(setPlant(value));
    localStorage.setItem("data_collection_plant", value);
    setValidationError("");
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: '#2D72D2',
          colorBgContainer: '#252A31',
          colorText: '#F5F8FA',
          colorBorder: '#383E47',
          borderRadius: 8,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        },
        components: {
          Button: {
            colorPrimary: '#2D72D2',
            colorPrimaryHover: '#4C90F0',
          },
          Select: {
            colorBgContainer: '#1C2127',
            colorBorder: '#383E47',
            colorTextPlaceholder: '#A7B6C2',
          },
          Card: {
            colorBgContainer: '#2F343C',
          },
          Alert: {
            colorWarning: '#FBB360',
            colorWarningBg: 'rgba(251, 179, 96, 0.1)',
            colorWarningBorder: 'rgba(251, 179, 96, 0.3)',
          },
        },
      }}
    >
      <div className="login-container">
        <div className="login-card">
          <div className="login-header">
            <img src={logoApp} alt="App Logo" className="login-logo" />
            <Title level={2} className="login-title">Data Collection App</Title>
            <Text className="login-subtitle">Select project/plant and login</Text>
          </div>

          <div className="login-form-container">
            {validationError && (
              <Alert
                message={validationError}
                type="warning"
                showIcon
                className="login-alert"
              />
            )}

            <Form layout="vertical">
              <Form.Item name="plant" className="login-form-item">
                <div className="plant-selector-container">
                  <PlantSelector value={plant} onChange={handlePlantChange} />
                </div>
              </Form.Item>

              <Form.Item className="login-form-item">
                <div className={`google-signin-container ${isLoginLoading ? 'login-loading' : ''}`}>
                  <GoogleSignInButton
                    handleCredentialResponse={handleCredentialResponse}
                    disabled={!plant}
                  />
                </div>
              </Form.Item>
            </Form>

            {/* Footer inside card */}
            <div style={{
              borderTop: '1px solid #383E47',
              paddingTop: '20px',
              marginTop: '20px',
              textAlign: 'center'
            }}>
              <Space direction="vertical" size="small">
                <Text style={{
                  color: '#A7B6C2',
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '6px'
                }}>
                  <img src={slackIcon} alt="Slack" style={{ width: '18px', height: '18px', filter: 'brightness(0) invert(1)' }} />
                  Need help? <a href={SLACK_LINK} target="_blank" rel="noopener noreferrer" style={{ color: '#2D72D2', textDecoration: 'none' }}>Contact support</a>
                </Text>
                <Text style={{ color: '#A7B6C2', fontSize: '11px', opacity: 0.6 }}>
                  VisualAIM Data Collection • v{DEPLOYMENT_DATE}
                </Text>
              </Space>
            </div>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default LoginApp;
