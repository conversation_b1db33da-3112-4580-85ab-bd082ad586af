import { createSlice } from "@reduxjs/toolkit";

const filesHandlerSlice = createSlice({
  name: "filesHandler",

  initialState: {
    selectedFiles: [],
    currentInspectionFiles: [],
    currentInspectionFilesPath: [],
    activeTab: "generalProperties",
    inspectionsGridApi: null,
    allCurrentInspectionsFiles: [],
    currentInspectionFilePath: [],
    currentAssetFiles: [],
    currentGeneralFile: [],
    allCurrentGeneralFiles: [],
    quickFilterText: "",
    activeModule: null,
    generalFilesGridApi: null,
    isLinkModalLoading: false,
    filesViewMode: "table", // "table" or "explorer"
  },

  reducers: {
    setSelectedFiles: (state, action) => {
      console.log("Setting selected files:", action.payload);
      state.selectedFiles = action.payload;
    },
    addSelectedFile: (state, action) => {
      const newFile = action.payload;
      if (!state.selectedFiles.some((file) => file.id === newFile.id)) {
        console.log("Adding selected file:", newFile);
        state.selectedFiles.push(newFile);
      }
    },
    removeSelectedFile: (state, action) => {
      const fileId = action.payload;
      console.log("Removing selected file with ID:", fileId);
      state.selectedFiles = state.selectedFiles.filter(
        (file) => file.id !== fileId
      );
      console.log("Updated selectedFiles list:", state.selectedFiles);
    },
    setCurrentInspectionFiles: (state, action) => {
      state.currentInspectionFiles = action.payload;
    },
    setCurrentInspectionFilesPath: (state, action) => {
      state.currentInspectionFilesPath = action.payload;
    },
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
      // Only clear when switching to specific tabs
      if (action.payload === "NCR" || action.payload === "generalProperties") {
        state.currentInspectionFilesPath = [];
      }
    },
    setInspectionsGridApi: (state, action) => {
      state.inspectionsGridApi = action.payload;
    },
    setAllCurrentInspectionsFiles: (state, action) => {
      state.allCurrentInspectionsFiles = action.payload;
    },
    setCurrentAssetFiles: (state, action) => {
      state.currentAssetFiles = action.payload;
    },
    setCurrentGeneralFile: (state, action) => {
      state.currentGeneralFile = action.payload;
    },
    setAllCurrentGeneralFiles: (state, action) => {
      state.allCurrentGeneralFiles = action.payload;
    },
    setQuickFilterText: (state, action) => {
      state.quickFilterText = action.payload;
    },
    setActiveModule: (state, action) => {
      state.activeModule = action.payload;
    },
    setGeneralFilesGridApi: (state, action) => {
      state.generalFilesGridApi = action.payload;
    },
    addFileToInspection: (state, action) => {
      const { path, category } = action.payload;
      state.currentInspectionFiles.push({ path, category });
    },
    removeFileFromInspection: (state, action) => {
      const path = action.payload;
      state.currentInspectionFiles = state.currentInspectionFiles.filter(
        (file) => file.path !== path
      );
    },
    setIsLinkModalLoading: (state, action) => {
      state.isLinkModalLoading = action.payload;
    },
    setFilesViewMode: (state, action) => {
      state.filesViewMode = action.payload;
    },
  },
});

export const {
  setSelectedFiles,
  addSelectedFile,
  removeSelectedFile,
  setCurrentInspectionFiles,
  setActiveTab,
  setInspectionsGridApi,
  setAllCurrentInspectionsFiles,
  setCurrentInspectionFilesPath,
  setCurrentAssetFiles,
  setCurrentGeneralFile,
  setAllCurrentGeneralFiles,
  setQuickFilterText,
  setActiveModule,
  setGeneralFilesGridApi,
  addFileToInspection,
  removeFileFromInspection,
  setIsLinkModalLoading,
  setFilesViewMode,
} = filesHandlerSlice.actions;

export default filesHandlerSlice.reducer;
