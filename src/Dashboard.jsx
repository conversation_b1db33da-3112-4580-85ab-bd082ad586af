import React, { useContext, useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Navbar,
  NavbarGroup,
  NavbarHeading,
  Alignment,
  Button,
  Icon,
  NavbarDivider,
  Position,
  Popover,
  Menu,
  MenuItem,

} from "@blueprintjs/core";
import { useNavigate } from 'react-router-dom';
import { Layout, Model } from "flexlayout-react";
import {
  BarChart,
  Bar,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { ResponsiveRadar } from '@nivo/radar';
import { DatePicker, Select, Form, Radio, ConfigProvider, theme } from 'antd';
import { AppContext } from "./AppContextProvider";
import { setIsLoggedIn, setUser } from "./redux/authSlice";
import { ResponsiveCalendar } from '@nivo/calendar';
import { AppToaster } from "./CustomComponents/AppToaster";
import axios from 'axios';
import dayjs from 'dayjs';
import logo from "./images/VisualAIM_icon.png";
import userIcon from "./images/developer.png";
import "./Styles/App.css";

const { OptGroup } = Select;

// Plants data structure from LoginApp
const PLANTS = [
  { display: "1 - TOLEDO", value: "TOLEDO", project: "GP" },
  { display: "2 - BRUNSWUICK", value: "BRUNSWUICK", project: "GP" },
  { display: "3 - MONTICELLO", value: "MONTICELLO", project: "GP" },
  { display: "4 - BIG ISLAND", value: "BIG ISLAND", project: "GP" },
  { display: "5 - ALABAMA RIVER", value: "ALABAMA RIVER", project: "GP" },
  { display: "6 - WAUNA", value: "WAUNA", project: "GP" },
  { display: "7 - NAHEOLA", value: "NAHEOLA", project: "GP" },
  { display: "8 - BREWTON", value: "BREWTON", project: "GP" },
  { display: "8.2 - BREWTON_2", value: "BREWTON_2", project: "GP" },
  { display: "9 - LEAF RIVER", value: "LEAF RIVER", project: "GP" },
  { display: "10 - CROSSETT", value: "CROSSETT", project: "GP" },
  { display: "11 - ASHDOWN", value: "ASHDOWN", project: "GP" },
  { display: "12 - FORDYCE", value: "FORDYCE", project: "GP" },
  { display: "13 - ARKADELPHIA", value: "ARKADELPHIA", project: "GP" },
  { display: "14 - PRATTVILLE", value: "PRATTVILLE", project: "GP" },
  { display: "15 - CEDAR SPRINGS", value: "CEDAR SPRINGS", project: "GP" },
  { display: "16 - PALATKA", value: "PALATKA", project: "GP" },
  { display: "17 - CLATSKANIE", value: "CLATSKANIE", project: "GP" },
  { display: "18 - HALSEY", value: "HALSEY", project: "GP" },
  { display: "19 - BAILEYVILLE", value: "BAILEYVILLE", project: "GP" },
  { display: "20 - BUCKSPORT", value: "BUCKSPORT", project: "GP" },
  { display: "21 - OLD TOWN", value: "OLD TOWN", project: "GP" },
  { display: "22 - MADISON", value: "MADISON", project: "GP" },
  { display: "23 - SKOWHEGAN", value: "SKOWHEGAN", project: "GP" },
  { display: "24 - JAY", value: "JAY", project: "GP" },
  { display: "25 - BERLIN", value: "BERLIN", project: "GP" },
  { display: "26 - GORHAM", value: "GORHAM", project: "GP" },
  { display: "27 - MILLINOCKET", value: "MILLINOCKET", project: "GP" },
  { display: "28 - EAST MILLINOCKET", value: "EAST MILLINOCKET", project: "GP" },
  { display: "29 - RUMFORD", value: "RUMFORD", project: "GP" },
  { display: "30 - WESTBROOK", value: "WESTBROOK", project: "GP" },
  { display: "31 - CHEMTRADE AUGUSTA", value: "CHEMTRADE AUGUSTA", project: "CHEMTRADE" },
  { display: "32 - CHEMTRADE NORTH VANCOUVER", value: "CHEMTRADE NORTH VANCOUVER", project: "CHEMTRADE" },
  { display: "33 - CHEMTRADE PRINCE GEORGE", value: "CHEMTRADE PRINCE GEORGE", project: "CHEMTRADE" },
  { display: "34 - ALLNEX PLANT 1", value: "ALLNEX PLANT 1", project: "ALLNEX" },
  { display: "35 - ALLNEX PLANT 2", value: "ALLNEX PLANT 2", project: "ALLNEX" }
];

// Group plants by project
const getPlantsByProject = () => {
  const projectGroups = {};

  PLANTS.forEach(plant => {
    if (!projectGroups[plant.project]) {
      projectGroups[plant.project] = [];
    }
    projectGroups[plant.project].push(plant);
  });

  return projectGroups;
};
// Duplicate imports removed - all imports moved to top
const { RangePicker } = DatePicker;
const { Option } = Select;

// Color mapping for graphs
const COLOR_MAP = {
  'Approved': '#018977',
  'Rejected': '#e07f9d',
  'Completed': '#486de8',
  'Resubmitted': '#b088f5'
  
};

const customColors = [
  '#486de8', '#e07f9d', '#f89256', "#d7f7f0", "#004D46", 
  '#b088f5', "#FBB360", "#68C1EE", "#8c8c94", "#e8d5ff", "#D0B090", '#018977'
];

// FlexLayout model configurations - OUTSIDE component to prevent re-rendering
const normalLayoutConfig = {
  global: {},
  borders: [],
  layout: {
    type: "row",
    weight: 100,
    children: [
      {
        type: "row",
        weight: 50,
        children: [
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Activity Calendar",
                component: "graph3",
                enableClose: false,
              }
            ]
          },
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Time Series",
                component: "graph1",
                enableClose: false,
              }
            ]
          }
        ]
      },
      {
        type: "row",
        weight: 50,
        children: [
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Cumulative Progress",
                component: "graph2",
                enableClose: false,
              }
            ]
          },
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Radar Chart",
                component: "graph4",
                enableClose: false,
              }
            ]
          }
        ]
      }
    ]
  }
};

const expandedLayoutConfig = {
  global: {},
  borders: [],
  layout: {
    type: "row",
    weight: 100,
    children: [
      {
        type: "row",
        weight: 50,
        children: [
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Activity Calendar",
                component: "graph3",
                enableClose: false,
              }
            ]
          },
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Time Series",
                component: "graph1",
                enableClose: false,
              }
            ]
          }
        ]
      },
      {
        type: "row",
        weight: 50,
        children: [
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Cumulative Progress",
                component: "graph2",
                enableClose: false,
              }
            ]
          },
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Radar Chart",
                component: "graph4",
                enableClose: false,
              },
              {
                type: "tab",
                name: "Components",
                component: "components",
                enableClose: false,
              }
            ]
          }
        ]
      }
    ]
  }
};

// Create models ONCE outside component - this is the key to prevent re-rendering!
const normalLayoutModel = Model.fromJson(normalLayoutConfig);
const expandedLayoutModel = Model.fromJson(expandedLayoutConfig);



// Update the getDefaultRange function to show all data
const getDefaultRange = () => {
  const startDate = dayjs('2024-07-26'); // Earliest available data
  const endDate = dayjs(); // Today
  return [startDate, endDate];
};

function Dashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isDarkTheme, setIsDarkTheme } = useContext(AppContext);
  const { user } = useSelector((state) => state.auth);
  const [form] = Form.useForm();



  // State declarations
  const [dateRange, setDateRange] = useState(getDefaultRange());
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitters, setSubmitters] = useState([]);
  const [selectedSubmitter, setSelectedSubmitter] = useState(['all']);
  const [selectedStep, setSelectedStep] = useState('all');
  const [uniqueSteps, setUniqueSteps] = useState([]);
  const [timeDistribution, setTimeDistribution] = useState('week');

  const [selectedStepType, setSelectedStepType] = useState('QCV'); // Add this new state
  const [plants, setPlants] = useState([]);
  const [selectedPlant, setSelectedPlant] = useState(['all']);
  const [cumulativeTimeDistribution, setCumulativeTimeDistribution] = useState('day');
  const [isExpanded, setIsExpanded] = useState(false); // Layout expansion state
  const [radarViewMode, setRadarViewMode] = useState('asset'); // 'asset' or 'user'

  // Fetch data and set submitters
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(`https://datacollectionapi.inspectionworkflow.com/asset_actions?days=365`);
        if (response.data) {
          const rawData = response.data.data || [];
          setData(rawData);
          
          // Extract unique submitters
          const uniqueSubmitters = [...new Set(rawData
            .map(item => item[8])
            .filter(Boolean)
          )].sort();
          
          // Extract unique steps (step is at index 9)
          const steps = [...new Set(rawData
            .map(item => item[9])
            .filter(Boolean)
          )].sort();
          
          // Extract unique plants (plant is at index 1)
          const uniquePlants = [...new Set(rawData
            .map(item => item[1])
            .filter(Boolean)
          )].sort();
          
          setSubmitters(uniqueSubmitters);
          setUniqueSteps(steps);
          setPlants(uniquePlants);
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Asset classification mapping function
  const mapAssetClassification = (classification) => {
    if (!classification) return classification;

    // Combine STORAGE TANK and TANK into STORAGE TANK
    if (classification === 'STORAGE TANK' || classification === 'TANK') {
      return 'STORAGE TANK';
    }

    // Combine HEAT EXCHANGER and FIRED EQUIPMENT into HEAT EXCHANGER
    if (classification === 'FIRED EQUIPMENT' || classification === 'HEAT EXCHANGER') {
      return 'HEAT EXCHANGER';
    }

    return classification;
  };

  // Process data for radar chart
  const processedData = React.useMemo(() => {
    if (!data.length) return { radarChartData: [], classifications: [] };

    // Apply consistent filters across all data
    const filteredData = data.filter(item => {
      if (!item || !Array.isArray(item)) return false;

      const itemDate = dayjs(item[7]); // timestamp is at index 7
      const dateInRange = itemDate.isAfter(dateRange[0]) && itemDate.isBefore(dateRange[1]);
      const categoryMatch = selectedCategory === 'all' || item[6] === selectedCategory; // action is at index 6

      // Updated submitter matching logic
      const submitterMatch = selectedSubmitter.includes('all') || selectedSubmitter.includes(item[8]);

      const stepMatch = selectedStep === 'all' || item[9] === selectedStep; // step is at index 9

      // Plant filter
      const plantMatch = selectedPlant.includes('all') || selectedPlant.includes(item[1]); // plant is at index 1

      return dateInRange && categoryMatch && submitterMatch && stepMatch && plantMatch;
    });

    // Get unique classifications with mapping applied
    const uniqueClassifications = [...new Set(filteredData
      .map(item => mapAssetClassification(item[4])) // asset_classification is at index 4, apply mapping
      .filter(Boolean))];

    // Process data for each submitter
    const submittersList = selectedSubmitter.includes('all') ? submitters : selectedSubmitter;
    
    const radarData = (submittersList || [])
      .filter(submitter => submitter && typeof submitter === 'string')
      .map(submitter => {
        const submitterData = filteredData.filter(item => item[8] === submitter);
        const total = submitterData.length;
        
        if (total === 0) return null;

        const counts = uniqueClassifications.reduce((acc, classification) => {
          const count = submitterData.filter(item => mapAssetClassification(item[4]) === classification).length;
          acc[classification] = count;
          return acc;
        }, {});

        const displayName = submitter.includes('@') 
          ? submitter.split('@')[0] 
          : submitter;

        return {
          submitter: displayName,
          ...counts
        };
      })
      .filter(Boolean); // Remove null entries

    return {
      radarChartData: radarData,
      classifications: uniqueClassifications
    };
  }, [data, dateRange, selectedCategory, selectedSubmitter, selectedStep, submitters, selectedPlant]);

  const handleSubmitterChange = (values) => {
    // If we're coming from 'all' and selecting something new
    if (selectedSubmitter.includes('all') && values.length > 1) {
      // Remove 'all' and keep only the newly selected value
      const newValues = values.filter(v => v !== 'all');
      setSelectedSubmitter(newValues);
      return;
    }

    // If selecting 'all'
    if (values.includes('all') && !selectedSubmitter.includes('all')) {
      setSelectedSubmitter(['all']);
      return;
    }

    // For all other cases, just update the selection normally
    setSelectedSubmitter(values);
  };

  const handlePlantChange = (values) => {
    // If we're coming from 'all' and selecting something new
    if (selectedPlant.includes('all') && values.length > 1) {
      // Remove 'all' and keep only the newly selected value
      const newValues = values.filter(v => v !== 'all');
      setSelectedPlant(newValues);
      return;
    }

    // If selecting 'all'
    if (values.includes('all') && !selectedPlant.includes('all')) {
      setSelectedPlant(['all']);
      return;
    }

    // For all other cases, just update the selection normally
    setSelectedPlant(values);
  };

  // Calculate key data points
  const calculateKeyDataPoints = () => {
    const filteredData = data.filter(item => {
      if (!item || !Array.isArray(item)) return false;
      const itemDate = dayjs(item[7]);
      const dateInRange = itemDate.isAfter(dateRange[0]) && itemDate.isBefore(dateRange[1]);
      const categoryMatch = selectedCategory === 'all' || item[6] === selectedCategory;
      const submitterMatch = selectedSubmitter.includes('all') || selectedSubmitter.includes(item[8]);
      const stepMatch = selectedStep === 'all' || item[9] === selectedStep;
      const plantMatch = selectedPlant.includes('all') || selectedPlant.includes(item[1]);
      return dateInRange && categoryMatch && submitterMatch && stepMatch && plantMatch;
    });

    const totalAssets = filteredData.length;
    const qcvAssets = filteredData.filter(item => item[9] === 'QCV').length;
    const uniquePlants = [...new Set(filteredData.map(item => item[1]).filter(Boolean))].length;
    const uniqueUsers = [...new Set(filteredData.map(item => item[8]).filter(Boolean))].length;
    const approvedAssets = filteredData.filter(item => item[6] === 'Approved').length;
    const rejectedAssets = filteredData.filter(item => item[6] === 'Rejected').length;

    return {
      totalAssets,
      qcvAssets,
      uniquePlants,
      uniqueUsers,
      approvedAssets,
      rejectedAssets
    };
  };

  // Key Data Points Panel
  const KeyDataPointsPanel = () => {
    const dataPoints = calculateKeyDataPoints();

    return (
      <div style={{
        width: '250px',
        backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9',
        borderRight: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
        padding: '20px',
        display: 'flex',
        flexDirection: 'column',
        gap: '15px'
      }}>
        <h3 style={{
          margin: '0 0 15px 0',
          color: isDarkTheme ? '#F5F8FA' : '#394B59',
          fontSize: '18px',
          fontWeight: '700',
          textAlign: 'center',
          borderBottom: `2px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
          paddingBottom: '10px'
        }}>
          Key Metrics
        </h3>

        {[
          { label: 'Total Assets', value: dataPoints.totalAssets, color: '#2D72D2', icon: '📊' },
          { label: 'QCV Assets', value: dataPoints.qcvAssets, color: '#018977', icon: '✅' },
          { label: 'Active Plants', value: dataPoints.uniquePlants, color: '#238551', icon: '🏭' },
          { label: 'Active Users', value: dataPoints.uniqueUsers, color: '#F29D49', icon: '👥' },
          { label: 'Approved', value: dataPoints.approvedAssets, color: '#018977', icon: '✓' },
          { label: 'Rejected', value: dataPoints.rejectedAssets, color: '#e07f9d', icon: '✗' }
        ].map((metric, index) => (
          <div key={index} style={{
            backgroundColor: isDarkTheme ? '#252A31' : '#ffffff',
            border: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
            borderRadius: '8px',
            padding: '15px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{
              fontSize: '24px',
              width: '40px',
              height: '40px',
              backgroundColor: metric.color + '20',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: `2px solid ${metric.color}`
            }}>
              {metric.icon}
            </div>
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '24px',
                fontWeight: '700',
                color: metric.color,
                lineHeight: '1'
              }}>
                {metric.value.toLocaleString()}
              </div>
              <div style={{
                fontSize: '12px',
                color: isDarkTheme ? '#A7B6C2' : '#6a737d',
                fontWeight: '500',
                marginTop: '2px'
              }}>
                {metric.label}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Form component with filters
  const FormContent = () => (
    <Form
      form={form}
      layout="inline"
      style={{ marginLeft: '20px' }}
      initialValues={{
        dateRange: getDefaultRange(),
        category: 'all',
        submitter: 'all',
        step: 'all',
        plant: 'all'
      }}
    >
      <Form.Item name="dateRange">
        <RangePicker
          style={{ width: '250px' }}
          onChange={(dates) => setDateRange(dates || getDefaultRange())}
          defaultValue={getDefaultRange()}
          disabledDate={(current) => current && current < dayjs('2024-07-26')}
        />
      </Form.Item>
      <Form.Item name="plant">
        <Select
          mode="multiple"
          value={selectedPlant}
          style={{ width: 200 }}
          onChange={handlePlantChange}
          showSearch
          optionFilterProp="children"
          maxTagCount="responsive"
          defaultValue={['all']}
          placeholder="Select Plants"
        >
          <Option value="all">All Plants</Option>
          {(() => {
            const projectGroups = getPlantsByProject();
            // Sort projects to place CHEMTRADE first, then ALLNEX, then GP, then others
            const sortedProjects = Object.keys(projectGroups).sort((a, b) => {
              if (a === "CHEMTRADE") return -1;
              if (b === "CHEMTRADE") return 1;
              if (a === "ALLNEX") return -1;
              if (b === "ALLNEX") return 1;
              if (a === "GP") return -1;
              if (b === "GP") return 1;
              return a.localeCompare(b);
            });

            return sortedProjects.map(project => (
              <OptGroup key={project} label={project}>
                {projectGroups[project].map((plant) => (
                  <Option key={plant.value} value={plant.value}>
                    {plant.display}
                  </Option>
                ))}
              </OptGroup>
            ));
          })()}
        </Select>
      </Form.Item>
      <Form.Item name="category">
        <Select
          defaultValue="all"
          style={{ width: 120 }}
          onChange={setSelectedCategory}
        >
          <Option value="all">All Categories</Option>
          <Option value="Approved">Approved</Option>
          <Option value="Rejected">Rejected</Option>
          <Option value="Completed">Completed</Option>
          <Option value="Resubmitted">Resubmitted</Option>
        </Select>
      </Form.Item>
      <Form.Item name="submitter">
        <Select
          mode="multiple"
          value={selectedSubmitter}
          style={{ width: 200 }}
          onChange={handleSubmitterChange}
          showSearch
          optionFilterProp="children"
          maxTagCount="responsive"
          defaultValue={['all']}
          placeholder="Select Users"
        >
          <Option value="all">All Users</Option>
          {submitters.map(submitter => (
            <Option key={submitter} value={submitter}>
              {submitter.split('@')[0]}
            </Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="step">
        <Select
          defaultValue="all"
          style={{ width: 120 }}
          onChange={setSelectedStep}
          showSearch
          optionFilterProp="children"
        >
          <Option value="all">All Steps</Option>
          {uniqueSteps.map(step => (
            <Option key={step} value={step}>
              {step}
            </Option>
          ))}
        </Select>
      </Form.Item>
    </Form>
  );

  // Factory function for graphs
  function factory(node) {
    const component = node.getComponent();

    if (loading) {
      return <div style={{ padding: '20px', textAlign: 'center', color: '#ffffff' }}>Loading...</div>;
    }

    switch (component) {
      case "graph4":
        if (!processedData.radarChartData.length) {
          return <div style={{ padding: '20px', textAlign: 'center', color: isDarkTheme ? '#ffffff' : '#000000' }}>
            No data available for the selected filters
          </div>;
        }

        // Process data for TreeMap - hierarchical breakdown: Plant → Asset Classification → Task → Submitter
        const filteredData = data.filter(item => {
          if (!item || !Array.isArray(item)) return false;
          const itemDate = dayjs(item[7]);
          const dateInRange = itemDate.isAfter(dateRange[0]) && itemDate.isBefore(dateRange[1]);
          const categoryMatch = selectedCategory === 'all' || item[6] === selectedCategory;
          const submitterMatch = selectedSubmitter.includes('all') || selectedSubmitter.includes(item[8]);
          const stepMatch = selectedStep === 'all' || item[9] === selectedStep;
          const plantMatch = selectedPlant.includes('all') || selectedPlant.includes(item[1]);
          return dateInRange && categoryMatch && submitterMatch && stepMatch && plantMatch;
        });

        console.log('Filtered data length:', filteredData.length);

        // Check if we have data
        if (filteredData.length === 0) {
          return (
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: isDarkTheme ? '#F5F8FA' : '#394B59',
              fontSize: '18px'
            }}>
              No data available for the selected filters
            </div>
          );
        }

        // Process data for radar chart - Ready, QC, QCV breakdown by asset classification
        const assetStepCounts = {};
        filteredData.forEach(item => {
          const assetClass = mapAssetClassification(item[4]) || 'Unknown Asset';
          const step = item[9] || 'Unknown Step'; // Step is at index 9

          if (!assetStepCounts[assetClass]) {
            assetStepCounts[assetClass] = {
              Ready: 0,
              QC: 0,
              QCV: 0
            };
          }

          if (step === 'Ready' || step === 'QC' || step === 'QCV') {
            assetStepCounts[assetClass][step]++;
          }
        });

        // Create radar chart data - each asset classification with Ready, QC, QCV counts
        const radarData = Object.entries(assetStepCounts).map(([asset, steps]) => ({
          asset: asset,
          Ready: steps.Ready,
          QC: steps.QC,
          QCV: steps.QCV
        }));

        console.log('Radar data:', radarData);

        return (
          <div style={{
            width: '100%',
            height: '100%',
            position: 'relative',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Header */}
            <div style={{
              padding: '10px 20px',
              borderBottom: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
              backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9'
            }}>
              <h3 style={{
                margin: 0,
                textAlign: 'center',
                color: isDarkTheme ? '#F5F8FA' : '#394B59',
                fontSize: '20px',
                fontWeight: '700'
              }}>
                Task Distribution by Asset Classification
              </h3>
              <p style={{
                margin: '5px 0 0 0',
                textAlign: 'center',
                color: isDarkTheme ? '#A7B6C2' : '#6a737d',
                fontSize: '13px'
              }}>
                Ready, QC, QCV breakdown across asset types
              </p>
            </div>

            <div style={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
                <div style={{ height: 'calc(100vh - 200px)', width: '100%', padding: '20px' }}>
                  <ResponsiveRadar
                    data={radarData}
                    keys={['Ready', 'QC', 'QCV']}
                    indexBy="asset"
                    valueFormat=">-.0f"
                    margin={{ top: 70, right: 80, bottom: 40, left: 80 }}
                    borderColor={{ from: 'color' }}
                    gridLabelOffset={36}
                    dotSize={10}
                    dotColor={{ theme: 'background' }}
                    dotBorderWidth={2}
                    colors={['#486de8', '#b088f5', '#018977']}
                    blendMode="multiply"
                    motionConfig="wobbly"
                    theme={{
                      background: isDarkTheme ? '#2F343C' : '#f6f7f9',
                      text: {
                        fontSize: 12,
                        fill: isDarkTheme ? '#F5F8FA' : '#394B59',
                        fontWeight: 600
                      },
                      tooltip: {
                        container: {
                          background: isDarkTheme ? '#252A31' : '#ffffff',
                          color: isDarkTheme ? '#F5F8FA' : '#394B59',
                          fontSize: '12px',
                          borderRadius: '4px',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                          border: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`
                        }
                      },
                      grid: {
                        line: {
                          stroke: isDarkTheme ? '#383E47' : '#d1d5da',
                          strokeWidth: 1
                        }
                      }
                    }}
                    legends={[
                      {
                        anchor: 'top-left',
                        direction: 'column',
                        translateX: -50,
                        translateY: -40,
                        itemWidth: 80,
                        itemHeight: 20,
                        itemTextColor: isDarkTheme ? '#F5F8FA' : '#394B59',
                        symbolSize: 12,
                        symbolShape: 'circle',
                        effects: [
                          {
                            on: 'hover',
                            style: {
                              itemTextColor: '#2D72D2'
                            }
                          }
                        ]
                      }
                    ]}
                  />
                </div>
            </div>
          </div>
        );

      case "graph1": {
        // First get the data filtered by selected step type
        const timeSeriesData = data.filter(item => {
          if (!item || !Array.isArray(item)) return false;
          
          const itemDate = dayjs(item[7]);
          const inDateRange = itemDate.isAfter(dateRange[0]) && itemDate.isBefore(dateRange[1]);
          const categoryMatch = selectedCategory === 'all' || item[6] === selectedCategory;
          const submitterMatch = selectedSubmitter.includes('all') || selectedSubmitter.includes(item[8]);
          const stepMatch = selectedStep === 'all' || item[9] === selectedStep;
          const stepTypeMatch = item[9] === selectedStepType;
          
          // Plant filter
          const plantMatch = selectedPlant.includes('all') || selectedPlant.includes(item[1]); // plant is at index 1
          
          return inDateRange && categoryMatch && submitterMatch && stepMatch && stepTypeMatch && plantMatch;
        });

        // Group by selected time distribution
        const groupedData = {};
        timeSeriesData.forEach(item => {
          let timeKey;
          const itemDate = dayjs(item[7]);

          switch(timeDistribution) {
            case 'week':
              const weekNum = itemDate.week();
              const yearNum = itemDate.year();
              timeKey = `${yearNum}-W${String(weekNum).padStart(2, '0')}`;
              break;
            case 'month':
              timeKey = itemDate.format('YYYY-MM');
              break;
            case 'year':
              timeKey = itemDate.format('YYYY');
              break;
            default:
              timeKey = itemDate.format('YYYY-MM-DD');
          }

          if (!groupedData[timeKey]) {
            groupedData[timeKey] = {
              approved: 0,
              rejected: 0,
              completed: 0,
              resubmitted: 0,
              total: 0
            };
          }

          // Count based on the action type
          if (selectedStepType === 'Ready') {
            if (item[6] === 'Completed') {
              groupedData[timeKey].completed++;
            } else if (item[6] === 'Resubmitted') {
              groupedData[timeKey].resubmitted++;
            }
          } else {
            if (item[6] === 'Approved') {
              groupedData[timeKey].approved++;
            } else if (item[6] === 'Rejected') {
              groupedData[timeKey].rejected++;
            }
          }
          groupedData[timeKey].total++;
        });

        // Convert to array format for plotting
        const chartData = Object.entries(groupedData)
          .map(([timeKey, counts]) => {
            // For weeks, create a proper sortable date string
            let sortKey;
            if (timeDistribution === 'week') {
              const [year, week] = timeKey.split('-W');
              // Create a date from the year and week number
              const weekDate = dayjs().year(parseInt(year)).week(parseInt(week)).startOf('week');
              sortKey = weekDate;
            } else {
              sortKey = dayjs(timeKey);
            }

            return {
              timeKey,
              sortKey,
              approved: counts.approved,
              rejected: counts.rejected,
              completed: counts.completed,
              resubmitted: counts.resubmitted,
              total: counts.total
            };
          })
          .sort((a, b) => a.sortKey.diff(b.sortKey))
          .map(({ timeKey, sortKey, ...rest }) => ({
            timeKey: timeDistribution === 'week'
              ? `${timeKey.split('-W')[0]} W${timeKey.split('-W')[1]}`  // Format as "2024 W01"
              : timeKey,
            ...rest
          }));



        return (
          <div style={{ 
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Time Range Controls */}
            <div style={{
              padding: '10px 20px',
              borderBottom: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
              backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9'
            }}>
              <div style={{ display: 'flex', gap: '15px', alignItems: 'center', flexWrap: 'wrap' }}>
                <span style={{
                  color: isDarkTheme ? '#F5F8FA' : '#394B59',
                  fontSize: '13px',
                  fontWeight: '600'
                }}>
                  Time Range:
                </span>
                <div style={{ display: 'flex', gap: '4px' }}>
                  {['day', 'week', 'month', 'year'].map((period) => (
                    <button
                      key={period}
                      onClick={() => setTimeDistribution(period)}
                      style={{
                        padding: '8px 16px',
                        border: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
                        borderRadius: '6px',
                        fontSize: '13px',
                        fontWeight: '600',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        background: timeDistribution === period
                          ? '#2D72D2'
                          : isDarkTheme ? '#2F343C' : '#ffffff',
                        color: timeDistribution === period
                          ? '#ffffff'
                          : isDarkTheme ? '#F5F8FA' : '#495057',
                        boxShadow: timeDistribution === period
                          ? '0 2px 8px rgba(45, 114, 210, 0.3)'
                          : 'none',
                        textTransform: 'capitalize'
                      }}
                      onMouseEnter={(e) => {
                        if (timeDistribution !== period) {
                          e.target.style.background = isDarkTheme ? '#383E47' : '#f6f7f9';
                          e.target.style.borderColor = '#2D72D2';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (timeDistribution !== period) {
                          e.target.style.background = isDarkTheme ? '#2F343C' : '#ffffff';
                          e.target.style.borderColor = isDarkTheme ? '#383E47' : '#d1d5da';
                        }
                      }}
                    >
                      {period === 'day' ? 'Days' : period === 'week' ? 'Weeks' : period === 'month' ? 'Months' : 'Years'}
                    </button>
                  ))}
                </div>
                <span style={{
                  color: isDarkTheme ? '#F5F8FA' : '#394B59',
                  fontSize: '13px',
                  fontWeight: '600',
                  marginLeft: '15px'
                }}>
                  Step Type:
                </span>
                <div style={{ display: 'flex', gap: '4px' }}>
                  {['Ready', 'QC', 'QCV'].map((stepType) => (
                    <button
                      key={stepType}
                      onClick={() => setSelectedStepType(stepType)}
                      style={{
                        padding: '8px 16px',
                        border: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
                        borderRadius: '6px',
                        fontSize: '13px',
                        fontWeight: '600',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        background: selectedStepType === stepType
                          ? '#2D72D2'
                          : isDarkTheme ? '#2F343C' : '#ffffff',
                        color: selectedStepType === stepType
                          ? '#ffffff'
                          : isDarkTheme ? '#F5F8FA' : '#495057',
                        boxShadow: selectedStepType === stepType
                          ? '0 2px 8px rgba(45, 114, 210, 0.3)'
                          : 'none',
                        minWidth: '60px'
                      }}
                      onMouseEnter={(e) => {
                        if (selectedStepType !== stepType) {
                          e.target.style.background = isDarkTheme ? '#383E47' : '#f6f7f9';
                          e.target.style.borderColor = '#2D72D2';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (selectedStepType !== stepType) {
                          e.target.style.background = isDarkTheme ? '#2F343C' : '#ffffff';
                          e.target.style.borderColor = isDarkTheme ? '#383E47' : '#d1d5da';
                        }
                      }}
                    >
                      {stepType}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              minHeight: 0
            }}>
                <h3 style={{
                  margin: '0 0 20px 0',
                  textAlign: 'center',
                  color: isDarkTheme ? '#F5F8FA' : '#394B59',
                  fontSize: '24px',
                  fontWeight: '700'
                }}>
                  {`${selectedStepType} Actions by ${timeDistribution.charAt(0).toUpperCase() + timeDistribution.slice(1)}`}
                </h3>
                <div style={{ height: 'calc(100vh - 200px)', width: '100%' }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={chartData.map(item => ({
                        timeKey: item.timeKey,
                        ...(selectedStepType === 'Ready' ? {
                          Completed: item.completed,
                          Resubmitted: item.resubmitted
                        } : {
                          [`${selectedStepType} Approved`]: item.approved,
                          [`${selectedStepType} Rejected`]: item.rejected
                        })
                      }))}
                      stackOffset="sign"
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke={isDarkTheme ? '#383E47' : '#d1d5da'} />
                      <XAxis
                        dataKey="timeKey"
                        tick={{
                          fill: isDarkTheme ? '#F5F8FA' : '#394B59',
                          fontSize: 12,
                          fontWeight: 500
                        }}
                        angle={-45}
                        textAnchor="end"
                        height={80}
                        interval={chartData.length > 8 ? Math.ceil(chartData.length / 6) - 1 : 0}
                      />
                      <YAxis
                        tick={{
                          fill: isDarkTheme ? '#F5F8FA' : '#394B59',
                          fontSize: 12,
                          fontWeight: 500
                        }}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: isDarkTheme ? '#252A31' : '#ffffff',
                          border: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
                          borderRadius: '4px',
                          color: isDarkTheme ? '#F5F8FA' : '#394B59'
                        }}
                      />
                      <Legend
                        wrapperStyle={{
                          color: isDarkTheme ? '#F5F8FA' : '#394B59',
                          fontSize: '14px',
                          fontWeight: '700'
                        }}
                      />
                      {selectedStepType === 'Ready' ? (
                        <>
                          <Bar dataKey="Completed" stackId="a" fill={COLOR_MAP.Completed} />
                          <Bar dataKey="Resubmitted" stackId="a" fill={COLOR_MAP.Resubmitted} />
                        </>
                      ) : (
                        <>
                          <Bar dataKey={`${selectedStepType} Approved`} stackId="a" fill={COLOR_MAP.Approved} />
                          <Bar dataKey={`${selectedStepType} Rejected`} stackId="a" fill={COLOR_MAP.Rejected} />
                        </>
                      )}
                    </BarChart>
                  </ResponsiveContainer>
                </div>
            </div>
          </div>
        );
      }

      case "graph2": {
        // Process data for cumulative line chart
        const cumulativeData = data.filter(item => {
          if (!item || !Array.isArray(item)) return false;

          const itemDate = dayjs(item[7]);
          const inDateRange = itemDate.isAfter(dateRange[0]) && itemDate.isBefore(dateRange[1]);
          const categoryMatch = selectedCategory === 'all' || item[6] === selectedCategory;
          const submitterMatch = selectedSubmitter.includes('all') || selectedSubmitter.includes(item[8]);
          const stepMatch = selectedStep === 'all' || item[9] === selectedStep;

          // Plant filter
          const plantMatch = selectedPlant.includes('all') || selectedPlant.includes(item[1]);

          return inDateRange && categoryMatch && submitterMatch && stepMatch && plantMatch;
        });

        // Group by time period and step type
        const timeData = {};
        cumulativeData.forEach(item => {
          let timeKey;
          const itemDate = dayjs(item[7]);

          switch(cumulativeTimeDistribution) {
            case 'minute':
              timeKey = itemDate.format('YYYY-MM-DD HH:mm');
              break;
            case 'hour':
              timeKey = itemDate.format('YYYY-MM-DD HH:00');
              break;
            case 'day':
              timeKey = itemDate.format('YYYY-MM-DD');
              break;
            case 'week':
              const weekNum = itemDate.week();
              const yearNum = itemDate.year();
              timeKey = `${yearNum}-W${String(weekNum).padStart(2, '0')}`;
              break;
            case 'month':
              timeKey = itemDate.format('YYYY-MM');
              break;
            case 'year':
              timeKey = itemDate.format('YYYY');
              break;
            default:
              timeKey = itemDate.format('YYYY-MM-DD');
          }

          const step = item[9]; // Step is at index 9 (Ready, QC, QCV)

          if (!timeData[timeKey]) {
            timeData[timeKey] = {
              Ready: 0,
              QC: 0,
              QCV: 0
            };
          }

          if (step === 'Ready' || step === 'QC' || step === 'QCV') {
            timeData[timeKey][step]++;
          }
        });

        // Convert to sorted array and calculate cumulative values
        const sortedTimeKeys = Object.keys(timeData).sort();
        let cumulativeReady = 0;
        let cumulativeQC = 0;
        let cumulativeQCV = 0;

        const chartData = sortedTimeKeys.map(timeKey => {
          cumulativeReady += timeData[timeKey].Ready;
          cumulativeQC += timeData[timeKey].QC;
          cumulativeQCV += timeData[timeKey].QCV;

          // Format display label based on time distribution
          let displayLabel;
          switch(cumulativeTimeDistribution) {
            case 'minute':
              displayLabel = dayjs(timeKey, 'YYYY-MM-DD HH:mm').format('MM/DD HH:mm');
              break;
            case 'hour':
              displayLabel = dayjs(timeKey, 'YYYY-MM-DD HH:00').format('MM/DD HH:00');
              break;
            case 'week':
              const [year, week] = timeKey.split('-W');
              displayLabel = `${year} W${week}`;
              break;
            case 'month':
              displayLabel = dayjs(timeKey, 'YYYY-MM').format('MMM YYYY');
              break;
            case 'year':
              displayLabel = timeKey;
              break;
            default:
              displayLabel = dayjs(timeKey).format('MM/DD/YYYY');
          }

          return {
            date: displayLabel,
            Ready: cumulativeReady,
            QC: cumulativeQC,
            QCV: cumulativeQCV
          };
        });



        return (
          <div style={{
            width: '100%',
            height: '100%',
            position: 'relative',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Time Range Controls */}
            <div style={{
              padding: '10px 20px',
              borderBottom: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
              backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9'
            }}>
              <div style={{ display: 'flex', gap: '15px', alignItems: 'center', flexWrap: 'wrap' }}>
                <span style={{
                  color: isDarkTheme ? '#F5F8FA' : '#394B59',
                  fontSize: '13px',
                  fontWeight: '600'
                }}>
                  Time Range:
                </span>
                <div style={{ display: 'flex', gap: '4px' }}>
                  {[
                    { value: 'minute', label: 'Minutes' },
                    { value: 'hour', label: 'Hours' },
                    { value: 'day', label: 'Days' },
                    { value: 'week', label: 'Weeks' },
                    { value: 'month', label: 'Months' },
                    { value: 'year', label: 'Years' }
                  ].map((period) => (
                    <button
                      key={period.value}
                      onClick={() => setCumulativeTimeDistribution(period.value)}
                      style={{
                        padding: '8px 16px',
                        border: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
                        borderRadius: '6px',
                        fontSize: '13px',
                        fontWeight: '600',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        background: cumulativeTimeDistribution === period.value
                          ? '#2D72D2'
                          : isDarkTheme ? '#2F343C' : '#ffffff',
                        color: cumulativeTimeDistribution === period.value
                          ? '#ffffff'
                          : isDarkTheme ? '#F5F8FA' : '#495057',
                        boxShadow: cumulativeTimeDistribution === period.value
                          ? '0 2px 8px rgba(45, 114, 210, 0.3)'
                          : 'none'
                      }}
                      onMouseEnter={(e) => {
                        if (cumulativeTimeDistribution !== period.value) {
                          e.target.style.background = isDarkTheme ? '#383E47' : '#f6f7f9';
                          e.target.style.borderColor = '#2D72D2';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (cumulativeTimeDistribution !== period.value) {
                          e.target.style.background = isDarkTheme ? '#2F343C' : '#ffffff';
                          e.target.style.borderColor = isDarkTheme ? '#383E47' : '#d1d5da';
                        }
                      }}
                    >
                      {period.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
            <div style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              minHeight: 0
            }}>
                <h3 style={{
                  margin: '0 0 20px 0',
                  textAlign: 'center',
                  color: isDarkTheme ? '#F5F8FA' : '#394B59',
                  fontSize: '24px',
                  fontWeight: '700'
                }}>
                  Cumulative Progress Over Time
                </h3>
                <div style={{ height: 'calc(100vh - 200px)', width: '100%' }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" stroke={isDarkTheme ? '#383E47' : '#d1d5da'} />
                      <XAxis
                        dataKey="date"
                        tick={{
                          fill: isDarkTheme ? '#F5F8FA' : '#394B59',
                          fontSize: 12,
                          fontWeight: 500
                        }}
                        angle={-45}
                        textAnchor="end"
                        height={80}
                        interval={chartData.length > 8 ? Math.ceil(chartData.length / 4) - 1 : 0}
                      />
                      <YAxis
                        tick={{
                          fill: isDarkTheme ? '#F5F8FA' : '#394B59',
                          fontSize: 12,
                          fontWeight: 500
                        }}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: isDarkTheme ? '#252A31' : '#ffffff',
                          border: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
                          borderRadius: '4px',
                          color: isDarkTheme ? '#F5F8FA' : '#394B59'
                        }}
                      />
                      <Legend
                        wrapperStyle={{
                          color: isDarkTheme ? '#F5F8FA' : '#394B59',
                          fontSize: '14px',
                          fontWeight: '700'
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="Ready"
                        stroke="#486de8"
                        strokeWidth={3}
                        fillOpacity={0.3}
                        fill="#486de8"
                        dot={false}
                        activeDot={{ r: 6, fill: '#486de8' }}
                        name="Ready (Cumulative)"
                      />
                      <Area
                        type="monotone"
                        dataKey="QC"
                        stroke={COLOR_MAP.Resubmitted}
                        strokeWidth={3}
                        fillOpacity={0.3}
                        fill={COLOR_MAP.Resubmitted}
                        dot={false}
                        activeDot={{ r: 6, fill: COLOR_MAP.Resubmitted }}
                        name="QC (Cumulative)"
                      />
                      <Area
                        type="monotone"
                        dataKey="QCV"
                        stroke="#018977"
                        strokeWidth={3}
                        fillOpacity={0.3}
                        fill="#018977"
                        dot={false}
                        activeDot={{ r: 6, fill: '#018977' }}
                        name="QCV (Cumulative)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
            </div>
          </div>
        );
      }

      case "graph3": {
        // Process data for GitHub-style heatmap calendar
        const calendarData = data.filter(item => {
          if (!item || !Array.isArray(item)) return false;

          const itemDate = dayjs(item[7]);
          const inDateRange = itemDate.isAfter(dateRange[0]) && itemDate.isBefore(dateRange[1]);
          const categoryMatch = selectedCategory === 'all' || item[6] === selectedCategory;
          const submitterMatch = selectedSubmitter.includes('all') || selectedSubmitter.includes(item[8]);
          const stepMatch = selectedStep === 'all' || item[9] === selectedStep;
          const plantMatch = selectedPlant.includes('all') || selectedPlant.includes(item[1]);

          return inDateRange && categoryMatch && submitterMatch && stepMatch && plantMatch;
        });

        // Group data by date and count activities
        const dailyData = {};
        calendarData.forEach(item => {
          const date = dayjs(item[7]).format('YYYY-MM-DD');
          if (!dailyData[date]) {
            dailyData[date] = 0;
          }
          dailyData[date]++;
        });

        // Convert to Nivo calendar format
        const calendarFormattedData = Object.entries(dailyData).map(([date, value]) => ({
          day: date,
          value: value
        }));

        // Get the date range for the calendar (last 12 months)
        const fromDate = dayjs().subtract(12, 'months').format('YYYY-MM-DD');
        const toDate = dayjs().format('YYYY-MM-DD');

        return (
          <div style={{
            width: '100%',
            height: '100%',
            position: 'relative',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9'
          }}>
            {/* Title */}
            <div style={{
              padding: '10px 20px',
              borderBottom: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
              backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9'
            }}>
              <h3 style={{
                margin: 0,
                color: isDarkTheme ? '#F5F8FA' : '#394B59',
                fontSize: '24px',
                fontWeight: '700',

                textAlign: 'center'
              }}>
                Activity Heatmap Calendar
              </h3>
            </div>

            {/* Calendar */}
            <div style={{
              flex: 1,
              position: 'relative',
              minHeight: 0,
              padding: '20px'
            }}>
              <ResponsiveCalendar
                data={calendarFormattedData}
                from={fromDate}
                to={toDate}
                emptyColor={isDarkTheme ? '#404854' : '#e5e5e5'}
                colors={[
                  'rgb(254, 246, 181)', // 0: Light yellow
                  'rgb(255, 221, 154)', // 1: Yellow-orange
                  'rgb(255, 194, 133)', // 2: Light orange
                  'rgb(255, 166, 121)', // 3: Orange
                  'rgb(250, 138, 118)', // 4: Orange-red
                  'rgb(241, 109, 122)', // 5: Red-pink
                  'rgb(225, 83, 131)'   // 6: Deep pink
                ]}
                margin={{ top: 20, right: 20, bottom: 60, left: 20 }}
                yearSpacing={50}
                monthBorderColor="transparent"
                dayBorderWidth={0}
                dayBorderColor="transparent"
                daySpacing={1}
                monthSpacing={8}
                legends={[
                  {
                    anchor: 'bottom',
                    direction: 'row',
                    translateY: 36,
                    itemCount: 5,
                    itemWidth: 15,
                    itemHeight: 15,
                    itemsSpacing: 4,
                    itemDirection: 'left-to-right',
                    symbolSize: 15,
                    symbolShape: 'square'
                  }
                ]}
                theme={{
                  background: isDarkTheme ? '#2F343C' : '#f6f7f9',
                  text: {
                    fontSize: 13,
                    fill: isDarkTheme ? '#F5F8FA' : '#394B59',
                    fontWeight: 500
                  },
                  labels: {
                    text: {
                      fontSize: 13,
                      fill: isDarkTheme ? '#F5F8FA' : '#394B59',
                      fontWeight: 500
                    }
                  }
                }}
                tooltip={({ day, value, color }) => (
                  <div style={{
                    background: isDarkTheme ? '#252A31' : '#ffffff',
                    padding: '12px 16px',
                    border: `2px solid ${isDarkTheme ? 'rgb(241, 109, 122)' : 'rgb(225, 83, 131)'}`,
                    borderRadius: '8px',
                    color: isDarkTheme ? 'rgb(255, 221, 154)' : 'rgb(225, 83, 131)',

                    fontSize: '13px',
                    fontWeight: '500',
                    boxShadow: isDarkTheme
                      ? '0 8px 24px rgba(241, 109, 122, 0.3)'
                      : '0 8px 24px rgba(225, 83, 131, 0.2)',
                    minWidth: '180px'
                  }}>
                    <div style={{
                      fontWeight: '700',
                      marginBottom: '6px',
                      color: isDarkTheme ? 'rgb(255, 194, 133)' : 'rgb(255, 166, 121)',
                      fontSize: '14px'
                    }}>
                      {dayjs(day).format('dddd, MMM DD, YYYY')}
                    </div>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '10px'
                    }}>
                      <div style={{
                        width: '14px',
                        height: '14px',
                        backgroundColor: color || (isDarkTheme ? '#2F343C' : '#f6f7f9'),
                        borderRadius: '2px',
                        border: color ? 'none' : `1px solid ${isDarkTheme ? '#555' : '#ccc'}`
                      }}></div>
                      <span style={{ fontWeight: '600' }}>
                        {value ? `${value} ${value === 1 ? 'activity' : 'activities'}` : 'No activities'}
                      </span>
                    </div>
                  </div>
                )}
              />
            </div>
          </div>
        );
      }

      case "components": {
        return (
          <div style={{
            width: '100%',
            height: '100%',
            position: 'relative',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9',
            padding: '20px'
          }}>
            {/* Components Header */}
            <div style={{
              padding: '10px 20px',
              borderBottom: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
              backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9',
              marginBottom: '20px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <h3 style={{
                  margin: 0,
                  color: isDarkTheme ? '#F5F8FA' : '#394B59',
                  fontSize: '18px',
                  fontWeight: '700',

                }}>
                  Component Properties
                </h3>
                <Button
                  icon="cross"
                  minimal={true}
                  onClick={() => setIsExpanded(false)}
                  style={{
                    color: isDarkTheme ? '#F5F8FA' : '#394B59'
                  }}
                />
              </div>
            </div>

            {/* Components Content */}
            <div style={{
              flex: 1,
              overflow: 'auto',
              backgroundColor: isDarkTheme ? '#252A31' : '#ffffff',
              borderRadius: '6px',
              border: `1px solid ${isDarkTheme ? '#383E47' : '#d1d5da'}`,
              padding: '20px'
            }}>
              <div style={{
                color: isDarkTheme ? '#F5F8FA' : '#394B59',

                fontSize: '14px'
              }}>
                <h4 style={{ marginTop: 0, color: isDarkTheme ? '#F5F8FA' : '#394B59' }}>
                  Equipment Data
                </h4>
                <div style={{ marginBottom: '20px' }}>
                  <h5 style={{ color: isDarkTheme ? '#A7B6C2' : '#6a737d' }}>Modules</h5>
                  <p>Module configuration and settings will appear here.</p>
                </div>
                <div>
                  <h5 style={{ color: isDarkTheme ? '#A7B6C2' : '#6a737d' }}>Component Properties</h5>
                  <p>Detailed component properties and metadata will be displayed here.</p>
                </div>
              </div>
            </div>
          </div>
        );
      }

      default:
        return null;
    }
  }

  // Toggle layout function
  const toggleLayout = () => {
    setIsExpanded(!isExpanded);
  };

  const userMenu = (
    <Menu className={isDarkTheme ? "bp5-dark" : ""}>
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text={`User: ${user?.name || ""}`}
        icon="user"
      />
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text="Logout"
        icon="log-out"
        onClick={() => {
          localStorage.removeItem("session_id");
          dispatch(setIsLoggedIn(false));
          dispatch(setUser(null));
          navigate('/login');
        }}
      />
    </Menu>
  );

  return (
    <div className={`app-container ${isDarkTheme ? "bp5-dark" : ""}`}>
      <Navbar
        fixedToTop={true}
        className={`${isDarkTheme ? "bp5-dark" : ""}`}
      >
        <NavbarGroup align={Alignment.LEFT} style={{ flexGrow: 1 }}>
          <NavbarHeading>
            <div className="heading-container" style={{ display: 'flex', alignItems: 'center' }}>
              <img src={logo} className="App-logo" alt="logo" width={45} />
              <span 
                style={{ 
                  marginLeft: '10px', 
                  color: isDarkTheme ? '#F5F8FA' : '#394B59',
                  fontSize: '18px',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
                onClick={() => {
                  navigator.clipboard.writeText("Team Stats");
                  AppToaster.show({
                    message: 'Copied "Team Stats" to clipboard',
                    intent: "success",
                    timeout: 2000
                  });
                }}
              >
                Team Stats
              </span>
            </div>
          </NavbarHeading>
          
          <ConfigProvider
            theme={{
              algorithm: isDarkTheme ? theme.darkAlgorithm : theme.defaultAlgorithm,
              token: {
                colorPrimary: '#2D72D2',
                colorBgContainer: isDarkTheme ? '#252A31' : '#f6f7f9',
                colorText: isDarkTheme ? '#F5F8FA' : '#000000',
                colorBorder: isDarkTheme ? '#383E47' : '#d9d9d9',
                borderRadius: 0,
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              },
              components: {
                Button: {
                  colorPrimary: '#2D72D2',
                  colorPrimaryHover: '#4C90F0',
                },
                Select: {
                  colorBgContainer: '#252A31',
                  colorBorder: '#252A31',
                  colorBorderHover: '#252A31',
                  colorBorderFocus: '#252A31',
                  colorTextPlaceholder: isDarkTheme ? '#A7B6C2' : '#595959',
                  colorText: '#FFFFFF',
                },
                Input: {
                  colorBgContainer: '#252A31',
                  colorBorder: '#252A31',
                  colorBorderHover: '#252A31',
                  colorBorderFocus: '#252A31',
                  colorText: '#FFFFFF',
                },
                Card: {
                  colorBgContainer: isDarkTheme ? '#2F343C' : '#f6f7f9',
                },
                Alert: {
                  colorWarning: '#FBB360',
                  colorWarningBg: isDarkTheme ? '#2F343C' : '#f6f7f9',
                  colorWarningBorder: '#FBB360',
                },
              },
            }}
          >
            <FormContent />
          </ConfigProvider>
        </NavbarGroup>

        <NavbarGroup align={Alignment.RIGHT}>
          <Button
            icon={<Icon icon="clipboard" />}
            minimal={true}
            onClick={() => navigate('/')}
          />
          <NavbarDivider />
          <Button
            icon={<Icon icon={isExpanded ? "collapse-all" : "expand-all"} />}
            minimal={true}
            onClick={toggleLayout}
            title={isExpanded ? "Collapse Components" : "Expand Components"}
            style={{
              color: isDarkTheme ? '#F5F8FA' : '#394B59'
            }}
          />
          <NavbarDivider />
          <Popover
            content={userMenu}
            position={Position.BOTTOM}
            interactionKind="hover"
            className={isDarkTheme ? "bp5-dark" : ""}
          >
            <div className="circle-image">
              <img src={user?.picture || userIcon} alt="User" />
            </div>
          </Popover>
          <NavbarDivider />
          <Button
            icon={
              <Icon
                icon={isDarkTheme ? "flash" : "moon"}
                style={{ color: isDarkTheme ? "#d39c43" : "#d39c43" }}
              />
            }
            minimal={true}
            onClick={() => setIsDarkTheme(!isDarkTheme)}
          />
          <NavbarDivider />
          <Button icon={<Icon icon="menu" />} minimal={true} />
        </NavbarGroup>
      </Navbar>

      <div
        className={`main-content ${isDarkTheme ? "" : "light-theme"} ${
          isDarkTheme ? "bp5-dark" : ""
        }`}
        style={{ 
          height: '100vh', 
          display: 'flex', 
          flexDirection: 'column',
          backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9'
        }}
      >
        <div style={{
          width: '100%',
          flex: 1,
          display: 'flex',
          overflow: 'hidden',
          marginTop: '50px'
        }}>
          <KeyDataPointsPanel />
          <div style={{
            flex: 1,
            padding: '10px',
            overflow: 'hidden'
          }}>
            <Layout
              model={isExpanded ? expandedLayoutModel : normalLayoutModel}
              factory={factory}
              className={isDarkTheme ? "flexlayout__layout-dark" : "flexlayout__layout-light"}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Dashboard;