import React, { useContext, useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Navbar,
  NavbarGroup,
  NavbarHeading,
  Alignment,
  Button,
  Icon,
  NavbarDivider,
  Position,
  Popover,
  Menu,
  MenuItem,

} from "@blueprintjs/core";
import { useNavigate } from 'react-router-dom';
import { Layout, Model } from "flexlayout-react";
import Plot from 'react-plotly.js';
import { DatePicker, Select, Space, Form, Radio } from 'antd';
import logo from "./images/VisualAIM_icon.png";
import userIcon from "./images/developer.png";
import "./Styles/App.css";
import { AppContext } from "./AppContextProvider";
import { setIsLoggedIn, setUser } from "./redux/authSlice";
import axios from 'axios';
import { format, parseISO } from 'date-fns';
import { ConfigProvider } from 'antd';
import { theme } from 'antd';
import dayjs from 'dayjs';
import { AppToaster } from "./CustomComponents/AppToaster";
// import { BarChart3, Calendar, ChartPie } from 'lucide-react';  // Add this import at the top
// import styles from Styles/App.css

import "./Styles/App.css";
const { RangePicker } = DatePicker;
const { Option } = Select;

// Color mapping for graphs
const COLOR_MAP = {
  'Approved': '#018977',
  'Rejected': '#e07f9d',
  'Completed': '#486de8',
  'Resubmitted': '#b088f5'
  
};

const customColors = [
  '#486de8', '#e07f9d', '#f89256', "#d7f7f0", "#004D46", 
  '#b088f5', "#FBB360", "#68C1EE", "#8c8c94", "#e8d5ff", "#D0B090", '#018977'
];

// FlexLayout model configuration
const initialLayoutModel = Model.fromJson({
  global: {},
  borders: [],
  layout: {
    type: "row",
    weight: 100,
    children: [
      {
        type: "row",
        weight: 50,
        children: [
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Radar Chart",
                component: "graph4",
              }
            ]
          },
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Time Series",
                component: "graph1",
              }
            ]
          }
        ]
      },
      {
        type: "row",
        weight: 50,
        children: [
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Scatter Plot",
                component: "graph2",
              }
            ]
          },
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Bar Chart",
                component: "graph3",
              }
            ]
          }
        ]
      }
    ]
  }
});

const STEPS = ['Ready', 'QC', 'QCV'];

// Update the getWeekRange function to use a fixed start date
const getWeekRange = () => {
  const today = dayjs();
  const monday = today.startOf('week').add(1, 'day'); // Adding 1 day because dayjs starts week on Sunday
  const sunday = monday.add(6, 'day').endOf('day'); // Get end of Sunday
  return [monday, sunday];
};

// Update the getDefaultRange function
const getDefaultRange = () => {
  const today = dayjs();
  const monday = today.startOf('week').add(1, 'day'); // Adding 1 day because dayjs starts week on Sunday
  const sunday = monday.add(6, 'day').endOf('day'); // Get end of Sunday
  return [monday, sunday];
};

function Dashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isDarkTheme, setIsDarkTheme } = useContext(AppContext);
  const { user } = useSelector((state) => state.auth);
  const [form] = Form.useForm();

  // Get current week's Monday and Sunday
  const getWeekRange = () => {
    const today = dayjs();
    const monday = today.startOf('week').add(1, 'day'); // Adding 1 day because dayjs starts week on Sunday
    const sunday = monday.add(6, 'day').endOf('day'); // Get end of Sunday
    return [monday, sunday];
  };

  // State declarations
  const [dateRange, setDateRange] = useState(getWeekRange());
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitters, setSubmitters] = useState([]);
  const [selectedSubmitter, setSelectedSubmitter] = useState(['all']);
  const [selectedStep, setSelectedStep] = useState('all');
  const [uniqueSteps, setUniqueSteps] = useState([]);
  const [timeDistribution, setTimeDistribution] = useState('day');
  const [displayMode, setDisplayMode] = useState('count');
  const [selectedStepType, setSelectedStepType] = useState('QCV'); // Add this new state
  const [plants, setPlants] = useState([]);
  const [selectedPlant, setSelectedPlant] = useState('all');

  // Fetch data and set submitters
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(`https://datacollectionapi.inspectionworkflow.com/asset_actions?days=365`);
        if (response.data) {
          const rawData = response.data.data || [];
          setData(rawData);
          
          // Extract unique submitters
          const uniqueSubmitters = [...new Set(rawData
            .map(item => item[8])
            .filter(Boolean)
          )].sort();
          
          // Extract unique steps (step is at index 9)
          const steps = [...new Set(rawData
            .map(item => item[9])
            .filter(Boolean)
          )].sort();
          
          // Extract unique plants (plant is at index 1)
          const uniquePlants = [...new Set(rawData
            .map(item => item[1])
            .filter(Boolean)
          )].sort();
          
          setSubmitters(uniqueSubmitters);
          setUniqueSteps(steps);
          setPlants(uniquePlants);
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Process data for radar chart
  const processedData = React.useMemo(() => {
    if (!data.length) return { radarChartData: [], classifications: [] };

    // Apply consistent filters across all data
    const filteredData = data.filter(item => {
      if (!item || !Array.isArray(item)) return false;
      
      const itemDate = dayjs(item[7]); // timestamp is at index 7
      const dateInRange = itemDate.isAfter(dateRange[0]) && itemDate.isBefore(dateRange[1]);
      const categoryMatch = selectedCategory === 'all' || item[6] === selectedCategory; // action is at index 6
      
      // Updated submitter matching logic
      const submitterMatch = selectedSubmitter.includes('all') || selectedSubmitter.includes(item[8]);
      
      const stepMatch = selectedStep === 'all' || item[9] === selectedStep; // step is at index 9
      
      // Plant filter
      const plantMatch = selectedPlant === 'all' || item[1] === selectedPlant; // plant is at index 1
      
      return dateInRange && categoryMatch && submitterMatch && stepMatch && plantMatch;
    });

    // Get unique classifications
    const uniqueClassifications = [...new Set(filteredData
      .map(item => item[4]) // asset_classification is at index 4
      .filter(Boolean))];

    // Process data for each submitter
    const submittersList = selectedSubmitter.includes('all') ? submitters : selectedSubmitter;
    
    const radarData = (submittersList || [])
      .filter(submitter => submitter && typeof submitter === 'string')
      .map(submitter => {
        const submitterData = filteredData.filter(item => item[8] === submitter);
        const total = submitterData.length;
        
        if (total === 0) return null;

        const counts = uniqueClassifications.reduce((acc, classification) => {
          const count = submitterData.filter(item => item[4] === classification).length;
          acc[classification] = count;
          return acc;
        }, {});

        const displayName = submitter.includes('@') 
          ? submitter.split('@')[0] 
          : submitter;

        return {
          submitter: displayName,
          ...counts
        };
      })
      .filter(Boolean); // Remove null entries

    return {
      radarChartData: radarData,
      classifications: uniqueClassifications
    };
  }, [data, dateRange, selectedCategory, selectedSubmitter, selectedStep, submitters, selectedPlant]);

  const handleSubmitterChange = (values) => {
    // If we're coming from 'all' and selecting something new
    if (selectedSubmitter.includes('all') && values.length > 1) {
      // Remove 'all' and keep only the newly selected value
      const newValues = values.filter(v => v !== 'all');
      setSelectedSubmitter(newValues);
      return;
    }
    
    // If selecting 'all'
    if (values.includes('all') && !selectedSubmitter.includes('all')) {
      setSelectedSubmitter(['all']);
      return;
    }
    
    // For all other cases, just update the selection normally
    setSelectedSubmitter(values);
  };

  // Form component with filters
  const FormContent = () => (
    <Form
      form={form}
      layout="inline"
      style={{ marginLeft: '20px' }}
      initialValues={{
        dateRange: getWeekRange(),
        category: 'all',
        submitter: 'all',
        step: 'all',
        plant: 'all'
      }}
    >
      <Form.Item name="dateRange">
        <RangePicker 
          style={{ width: '250px' }}
          onChange={(dates) => setDateRange(dates || getWeekRange())}
          defaultValue={getWeekRange()}
          disabledDate={(current) => current && current < dayjs('2024-07-26')}
        />
      </Form.Item>
      <Form.Item name="plant">
        <Select
          defaultValue="all"
          style={{ width: 120 }}
          onChange={setSelectedPlant}
          showSearch
          optionFilterProp="children"
        >
          <Option value="all">All Plants</Option>
          {plants.map(plant => (
            <Option key={plant} value={plant}>
              {plant}
            </Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="category">
        <Select
          defaultValue="all"
          style={{ width: 120 }}
          onChange={setSelectedCategory}
        >
          <Option value="all">All Categories</Option>
          <Option value="Approved">Approved</Option>
          <Option value="Rejected">Rejected</Option>
          <Option value="Completed">Completed</Option>
          <Option value="Resubmitted">Resubmitted</Option>
        </Select>
      </Form.Item>
      <Form.Item name="submitter">
        <Select
          mode="multiple"
          value={selectedSubmitter}
          style={{ width: 200 }}
          onChange={handleSubmitterChange}
          showSearch
          optionFilterProp="children"
          maxTagCount="responsive"
          defaultValue={['all']}
          placeholder="Select Users"
        >
          <Option value="all">All Users</Option>
          {submitters.map(submitter => (
            <Option key={submitter} value={submitter}>
              {submitter.split('@')[0]}
            </Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="step">
        <Select
          defaultValue="all"
          style={{ width: 120 }}
          onChange={setSelectedStep}
          showSearch
          optionFilterProp="children"
        >
          <Option value="all">All Steps</Option>
          {uniqueSteps.map(step => (
            <Option key={step} value={step}>
              {step}
            </Option>
          ))}
        </Select>
      </Form.Item>
    </Form>
  );

  // Factory function for graphs
  const factory = (node) => {
    const component = node.getComponent();

    if (loading) {
      return <div style={{ padding: '20px', textAlign: 'center', color: '#ffffff' }}>Loading...</div>;
    }

    switch (component) {
      case "graph4":
        if (!processedData.radarChartData.length) {
          return <div style={{ padding: '20px', textAlign: 'center', color: isDarkTheme ? '#ffffff' : '#000000' }}>
            No data available for the selected filters
          </div>;
        }

        // Calculate total submissions per submitter
        const submitterTotals = processedData.radarChartData.reduce((acc, item) => {
          const total = Object.entries(item)
            .filter(([key]) => key !== 'submitter')
            .reduce((sum, [_, value]) => sum + value, 0);
          acc[item.submitter] = total;
          return acc;
        }, {});

        return (
          <div style={{ 
            width: '100%', 
            height: '100%', 
            position: 'relative',
            display: 'flex'
          }}>
            <Plot
              data={processedData.radarChartData.map((item, index) => ({
                type: 'scatterpolar',
                name: `${item.submitter} (${submitterTotals[item.submitter]} total)`,
                r: processedData.classifications.map(classification => 
                  item[classification] || 0
                ),
                theta: processedData.classifications,
                fill: 'toself',
                opacity: 0.7,
                legendgroup: item.submitter,
                showlegend: true,
                hovertemplate: 
                  '%{theta}<br>' +
                  '%{r}<br>' +
                  '<extra></extra>',
                line: {
                  width: 2,
                  shape: 'linear'
                },
                marker: {
                  color: customColors[index % customColors.length]
                },
                fillcolor: customColors[index % customColors.length]
              })).concat(
                // Add invisible traces for additional legend entries with actual counts
                processedData.radarChartData.flatMap((item, index) => 
                  processedData.classifications.map(classification => ({
                    type: 'scatterpolar',
                    name: `${classification}: ${item[classification] || 0}`,
                    r: [0],
                    theta: [processedData.classifications[0]],
                    showlegend: true,
                    legendgroup: item.submitter,
                    legendgrouptitle: {
                      text: `${item.submitter} (${submitterTotals[item.submitter]} total)`
                    },
                    line: {
                      width: 2
                    },
                    marker: {
                      color: customColors[index % customColors.length]
                    },
                    opacity: 0
                  }))
                )
              )}
              layout={{
                gridshape: 'linear',
                paper_bgcolor: isDarkTheme ? '#2F343C' : '#f6f7f9',
                plot_bgcolor: isDarkTheme ? '#252A31' : '#f6f7f9',
                font: {
                  family: 'JetBrains Mono, monospace',
                  size: 14,
                  color: isDarkTheme ? '#F5F8FA' : '#394B59'
                },
                title: {
                  text: 'Asset Classification Distribution by User',
                  x: 0.5,
                  font: { 
                    family: 'JetBrains Mono, monospace',
                    size: 24,
                    color: isDarkTheme ? '#F5F8FA' : '#394B59',
                    weight: 700
                  }
                },
                showlegend: true,
                legend: {
                  orientation: 'v',
                  y: 1,
                  x: 1.2,
                  xanchor: 'left',
                  font: {
                    family: 'JetBrains Mono, monospace',
                    size: 13,
                    color: isDarkTheme ? '#F5F8FA' : '#394B59',
                    weight: 600
                  },
                  bgcolor: 'rgba(0,0,0,0)',
                  bordercolor: 'rgba(0,0,0,0)',
                  groupclick: 'toggleitem',
                  grouptitlefont: {
                    family: 'JetBrains Mono, monospace',
                    size: 14,
                    color: isDarkTheme ? '#F5F8FA' : '#394B59',
                    weight: 700
                  }
                },
                polar: {
                  radialaxis: {
                    visible: true,
                    tickformat: 'd',
                    gridcolor: 'black',
                    linecolor: 'black',
                    tickcolor: 'black',
                    gridwidth: 1,
                    tickfont: {
                      family: 'JetBrains Mono, monospace',
                      size: 12,
                      weight: 700
                    }
                  },
                  angularaxis: {
                    visible: true,
                    gridcolor: 'black',
                    linecolor: 'black',
                    tickcolor: 'black',
                    gridwidth: 1,
                    tickfont: {
                      family: 'JetBrains Mono, monospace',
                      size: 12,
                      weight: 700
                    }
                  },
                  bgcolor: isDarkTheme ? '#252A31' : '#FFFFFF'
                },
                margin: { t: 50, r: 200, b: 50, l: 80 },
                dragmode: false,
                autosize: true
              }}
              config={{ 
                responsive: true,
                displayModeBar: false, // Hide the mode bar
                staticPlot: false, // Allow interactivity but make text selectable
              }}
              style={{
                width: '100%',
                height: '100%',
                position: 'absolute',
                top: 0,
                left: 0
              }}
              useResizeHandler={true}
            />
          </div>
        );

      case "graph1": {
        // First get the data filtered by selected step type
        const timeSeriesData = data.filter(item => {
          if (!item || !Array.isArray(item)) return false;
          
          const itemDate = dayjs(item[7]);
          const inDateRange = itemDate.isAfter(dateRange[0]) && itemDate.isBefore(dateRange[1]);
          const categoryMatch = selectedCategory === 'all' || item[6] === selectedCategory;
          const submitterMatch = selectedSubmitter.includes('all') || selectedSubmitter.includes(item[8]);
          const stepMatch = selectedStep === 'all' || item[9] === selectedStep;
          const stepTypeMatch = item[9] === selectedStepType;
          
          // Plant filter
          const plantMatch = selectedPlant === 'all' || item[1] === selectedPlant; // plant is at index 1
          
          return inDateRange && categoryMatch && submitterMatch && stepMatch && stepTypeMatch && plantMatch;
        });

        // Group by selected time distribution
        const groupedData = {};
        timeSeriesData.forEach(item => {
          let timeKey;
          const itemDate = dayjs(item[7]);

          switch(timeDistribution) {
            case 'week':
              const weekNum = itemDate.week();
              const yearNum = itemDate.year();
              timeKey = `${yearNum}-W${String(weekNum).padStart(2, '0')}`;
              break;
            case 'month':
              timeKey = itemDate.format('YYYY-MM');
              break;
            case 'year':
              timeKey = itemDate.format('YYYY');
              break;
            default:
              timeKey = itemDate.format('YYYY-MM-DD');
          }

          if (!groupedData[timeKey]) {
            groupedData[timeKey] = {
              approved: 0,
              rejected: 0,
              completed: 0,
              resubmitted: 0,
              total: 0
            };
          }

          // Count based on the action type
          if (selectedStepType === 'Ready') {
            if (item[6] === 'Completed') {
              groupedData[timeKey].completed++;
            } else if (item[6] === 'Resubmitted') {
              groupedData[timeKey].resubmitted++;
            }
          } else {
            if (item[6] === 'Approved') {
              groupedData[timeKey].approved++;
            } else if (item[6] === 'Rejected') {
              groupedData[timeKey].rejected++;
            }
          }
          groupedData[timeKey].total++;
        });

        // Convert to array format for plotting
        const chartData = Object.entries(groupedData)
          .map(([timeKey, counts]) => {
            // For weeks, create a proper sortable date string
            let sortKey;
            if (timeDistribution === 'week') {
              const [year, week] = timeKey.split('-W');
              // Create a date from the year and week number
              const weekDate = dayjs().year(parseInt(year)).week(parseInt(week)).startOf('week');
              sortKey = weekDate;
            } else {
              sortKey = dayjs(timeKey);
            }

            return {
              timeKey,
              sortKey,
              approved: counts.approved,
              rejected: counts.rejected,
              completed: counts.completed,
              resubmitted: counts.resubmitted,
              total: counts.total
            };
          })
          .sort((a, b) => a.sortKey.diff(b.sortKey))
          .map(({ timeKey, sortKey, ...rest }) => ({
            timeKey: timeDistribution === 'week'
              ? `${timeKey.split('-W')[0]} W${timeKey.split('-W')[1]}`  // Format as "2024 W01"
              : timeKey,
            ...rest
          }));

        // Create bar chart data based on selected step type
        const barChartData = selectedStepType === 'Ready' ? [
          {
            type: 'bar',
            name: 'Completed',
            x: chartData.map(d => d.timeKey),
            y: chartData.map(d => d.completed),
            marker: { color: COLOR_MAP.Completed },
            text: chartData.map(d => d.completed),
            textposition: 'inside',
            insidetextanchor: 'middle',
            textangle: 0
          },
          {
            type: 'bar',
            name: 'Resubmitted',
            x: chartData.map(d => d.timeKey),
            y: chartData.map(d => d.resubmitted),
            marker: { color: COLOR_MAP.Resubmitted },
            text: chartData.map(d => d.resubmitted),
            textposition: 'inside',
            insidetextanchor: 'middle',
            textangle: 0
          }
        ] : [
          {
            type: 'bar',
            name: `${selectedStepType} Approved`,
            x: chartData.map(d => d.timeKey),
            y: chartData.map(d => d.approved),
            marker: { color: COLOR_MAP.Approved },
            text: chartData.map(d => d.approved),
            textposition: 'inside',
            insidetextanchor: 'middle',
            textangle: 0
          },
          {
            type: 'bar',
            name: `${selectedStepType} Rejected`,
            x: chartData.map(d => d.timeKey),
            y: chartData.map(d => d.rejected),
            marker: { color: COLOR_MAP.Rejected },
            text: chartData.map(d => d.rejected),
            textposition: 'inside',
            insidetextanchor: 'middle',
            textangle: 0
          }
        ];

        return (
          <div style={{ 
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{ 
              flexShrink: 0,
              display: 'flex', 
              flexDirection: 'column',
              alignItems: 'center',
              gap: '12px',
              padding: '10px 15px',
              marginBottom: '10px'
            }}>
              <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                <Radio.Group 
                  defaultValue="day"
                  buttonStyle="solid"
                  onChange={(e) => setTimeDistribution(e.target.value)}
                  value={timeDistribution}
                  style={{
                    display: 'flex',
                    justifyContent: 'center'
                  }}
                >
                  {['Days', 'Weeks', 'Months', 'Year'].map(period => (
                    <Radio.Button 
                      key={period.toLowerCase()}
                      value={period.toLowerCase().replace(/s$/, '')}
                      style={{ 
                        minWidth: '80px',
                        fontFamily: 'var(--app-font)',
                        fontWeight: 500
                      }}
                    >
                      {period}
                    </Radio.Button>
                  ))}
                </Radio.Group>
              </div>

              <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                <Radio.Group 
                  buttonStyle="solid"
                  onChange={(e) => setSelectedStepType(e.target.value)}
                  value={selectedStepType}
                  style={{
                    display: 'flex',
                    justifyContent: 'center'
                  }}
                >
                  {['Ready', 'QC', 'QCV'].map(step => (
                    <Radio.Button 
                      key={step}
                      value={step}
                      style={{ 
                        minWidth: '80px',
                        fontFamily: 'var(--app-font)',
                        fontWeight: 500
                      }}
                    >
                      {step}
                    </Radio.Button>
                  ))}
                </Radio.Group>
              </div>
            </div>

            <div style={{ 
              flex: 1,
              position: 'relative',
              minHeight: 0
            }}>
              <Plot
                data={barChartData}
                layout={{
                  paper_bgcolor: isDarkTheme ? '#2F343C' : '#f6f7f9',
                  plot_bgcolor: isDarkTheme ? '#252A31' : '#f6f7f9',
                  barmode: 'stack',
                  bargap: 0.3,
                  font: {
                    family: 'JetBrains Mono, monospace',
                    size: 14,
                    color: isDarkTheme ? '#F5F8FA' : '#394B59'
                  },
                  title: {
                    text: `${selectedStepType} Actions by ${timeDistribution.charAt(0).toUpperCase() + timeDistribution.slice(1)}`,
                    font: {
                      family: 'JetBrains Mono, monospace',
                      size: 24,
                      color: isDarkTheme ? '#F5F8FA' : '#394B59',
                      weight: 700
                    }
                  },
                  xaxis: {
                    title: 'Date',
                    tickfont: {
                      family: 'JetBrains Mono, monospace',
                      size: 12,
                      color: isDarkTheme ? '#F5F8FA' : '#394B59',
                      weight: 600
                    },
                    automargin: true, // This will automatically adjust margins
                    tickangle: 'auto', // This will let Plotly decide the angle
                    linecolor: 'black',
                    linewidth: 2,
                    mirror: true,
                    gridcolor: 'black',
                    gridwidth: 1,
                    showgrid: true
                  },
                  yaxis: {
                    title: 'Number of Actions',
                    tickfont: {
                      family: 'JetBrains Mono, monospace',
                      size: 12,
                      color: isDarkTheme ? '#F5F8FA' : '#394B59',
                      weight: 600
                    },
                    linecolor: 'black',
                    linewidth: 2,
                    mirror: true,
                    gridcolor: 'black',
                    gridwidth: 1,
                    showgrid: true
                  },
                  showlegend: true,
                  legend: {
                    font: {
                      family: 'JetBrains Mono, monospace',
                      size: 13,
                      color: isDarkTheme ? '#F5F8FA' : '#394B59',
                      weight: 600
                    },
                    bgcolor: 'rgba(0,0,0,0)'
                  },
                  margin: { t: 50, r: 20, b: 100, l: 60 },
                  autosize: true
                }}
                config={{
                  responsive: true,
                  displayModeBar: false
                }}
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'absolute',
                  top: 0,
                  left: 0
                }}
                useResizeHandler={true}
              />
            </div>
          </div>
        );
      }

      default:
        return null;
    }
  };

  const userMenu = (
    <Menu className={isDarkTheme ? "bp5-dark" : ""}>
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text={`User: ${user?.name || ""}`}
        icon="user"
      />
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text="Logout"
        icon="log-out"
        onClick={() => {
          localStorage.removeItem("session_id");
          dispatch(setIsLoggedIn(false));
          dispatch(setUser(null));
          navigate('/login');
        }}
      />
    </Menu>
  );

  return (
    <div className={`app-container ${isDarkTheme ? "bp5-dark" : ""}`}>
      <Navbar
        fixedToTop={true}
        className={`${isDarkTheme ? "bp5-dark" : ""}`}
      >
        <NavbarGroup align={Alignment.LEFT} style={{ flexGrow: 1 }}>
          <NavbarHeading>
            <div className="heading-container" style={{ display: 'flex', alignItems: 'center' }}>
              <img src={logo} className="App-logo" alt="logo" width={45} />
              <span 
                style={{ 
                  marginLeft: '10px', 
                  color: isDarkTheme ? '#F5F8FA' : '#394B59',
                  fontSize: '18px',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
                onClick={() => {
                  navigator.clipboard.writeText("Team Stats");
                  AppToaster.show({
                    message: 'Copied "Team Stats" to clipboard',
                    intent: "success",
                    timeout: 2000
                  });
                }}
              >
                Team Stats
              </span>
            </div>
          </NavbarHeading>
          
          <ConfigProvider
            theme={{
              algorithm: isDarkTheme ? theme.darkAlgorithm : theme.defaultAlgorithm,
              token: {
                colorPrimary: '#2D72D2',
                colorBgContainer: isDarkTheme ? '#252A31' : '#f6f7f9',
                colorText: isDarkTheme ? '#F5F8FA' : '#000000',
                colorBorder: isDarkTheme ? '#383E47' : '#d9d9d9',
                borderRadius: 0,
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              },
              components: {
                Button: {
                  colorPrimary: '#2D72D2',
                  colorPrimaryHover: '#4C90F0',
                },
                Select: {
                  colorBgContainer: isDarkTheme ? '#252A31' : '#f6f7f9',
                  colorBorder: isDarkTheme ? '#383E47' : '#d9d9d9',
                  colorTextPlaceholder: isDarkTheme ? '#A7B6C2' : '#595959',
                  colorText: isDarkTheme ? '#F5F8FA' : '#000000',
                },
                Card: {
                  colorBgContainer: isDarkTheme ? '#2F343C' : '#f6f7f9',
                },
                Alert: {
                  colorWarning: '#FBB360',
                  colorWarningBg: isDarkTheme ? '#2F343C' : '#f6f7f9',
                  colorWarningBorder: '#FBB360',
                },
              },
            }}
          >
            <FormContent />
          </ConfigProvider>
        </NavbarGroup>

        <NavbarGroup align={Alignment.RIGHT}>
          <Button
            icon={<Icon icon="clipboard" />}
            minimal={true}
            onClick={() => navigate('/')}
          />
          <NavbarDivider />
          <Popover
            content={userMenu}
            position={Position.BOTTOM}
            interactionKind="hover"
            className={isDarkTheme ? "bp5-dark" : ""}
          >
            <div className="circle-image">
              <img src={user?.picture || userIcon} alt="User" />
            </div>
          </Popover>
          <NavbarDivider />
          <Button
            icon={
              <Icon
                icon={isDarkTheme ? "flash" : "moon"}
                style={{ color: isDarkTheme ? "#d39c43" : "#d39c43" }}
              />
            }
            minimal={true}
            onClick={() => setIsDarkTheme(!isDarkTheme)}
          />
          <NavbarDivider />
          <Button icon={<Icon icon="menu" />} minimal={true} />
        </NavbarGroup>
      </Navbar>

      <div
        className={`main-content ${isDarkTheme ? "" : "light-theme"} ${
          isDarkTheme ? "bp5-dark" : ""
        }`}
        style={{ 
          height: '100vh', 
          display: 'flex', 
          flexDirection: 'column',
          backgroundColor: isDarkTheme ? '#2F343C' : '#f6f7f9'
        }}
      >
        <div style={{ 
          width: '100%', 
          flex: 1,
          padding: '10px',
          overflow: 'hidden',
          marginTop: '50px'
        }}>
          <Layout
            model={initialLayoutModel}
            factory={factory}
            className={isDarkTheme ? "flexlayout__layout-dark" : "flexlayout__layout-light"}
          />
        </div>
      </div>
    </div>
  );
}

export default Dashboard;