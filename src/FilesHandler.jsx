import React, { useState, useEffect, useRef, useMemo } from "react";
import { Layout, Model, Actions as FlexLayoutActions } from "flexlayout-react";
import {
  <PERSON><PERSON>,
  Card,
  Button,
  NonIdealState,
  Dialog,
  Intent,
  MenuItem,
  Spinner,
  Tag,
  Toaster,
  Position
} from "@blueprintjs/core";
import { Select } from "@blueprintjs/select";
import PDFJsViewer from "./PDFJsViewer2";
import ImageViewer from "./ImageViewer";
import { useSelector, useDispatch } from "react-redux";
import "primereact/resources/themes/fluent-light/theme.css";
import "primereact/resources/primereact.min.css";
import "primeicons/primeicons.css";
import FilesTable from "./FilesTable/FilesTable";
import VSCodeTreeExplorer from "./FileViewer/FileExplorer";
import { updateInspectionData } from "./redux/inspectionDataSlice";
import { useUpdateCell } from "./hooks/useUpdateCell";
import { Divider } from "antd";
import axios from "axios";
import { Bot } from "lucide-react";
import {
  setSelectedFiles,
  addSelectedFile,
  setCurrentGeneralFile,
  addFileToInspection,
  removeFileFromInspection,
} from "./redux/filesHandlerSlice";
import GenAIResultsTable from "./GenAIResultsTable/GenAIResultsTable";

// Create a context for AI loading state
export const AILoadingContext = React.createContext({
  isAILoading: false,
  setIsAILoading: () => {}
});

// Create a toaster instance outside of the component
const AppToaster = Toaster.create({
  position: Position.TOP,
});

// Set this to false to disable AI features, or true to enable them
const ENABLE_AI_FEATURES = false;

// Global function to reload Previous Inspections data
window.reloadPreviousInspectionsData = () => {
  console.log("Global reload function called, but not yet implemented");
};

const initialLayoutJson = {
  global: { tabContentClassName: "custom-tab" },
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        id: "viewer-tabset",
        weight: 67,
        selected: 0,
        children: [
          {
            type: "tab",
            component: "mainContent",
            name: "Viewer",
            id: "main-viewer-tab",
            enableClose: true,
            enableRename: false,
            tabSetEnableDivide: false,
          },
        ],
      },
      {
        type: "tabset",
        id: "files-tabset",
        weight: 33,
        children: [
          {
            type: "tab",
            component: "sidebar",
            name: "Files Table",
            id: "files-table-tab",
            enableClose: false,
            enableRename: false,
          },
          {
            type: "tab",
            component: "fileExplorer",
            name: "File Explorer",
            id: "file-explorer-tab",
            enableClose: false,
            enableRename: false,
          },
        ],
      },
    ],
  },
};

function FilesHandler({ isDarkTheme, windowMode, ownerDocument, panZoomRef }) {
  const dispatch = useDispatch();
  const {
    selectedFiles,
    currentGeneralFile,
  } = useSelector((state) => state.filesHandler);
  const selectedRowData = useSelector(
    (state) => state.selectedRows["inspections-grid"]
  );

  const user = useSelector((state) => state.auth.user);
  const { updateCell, isLoading } = useUpdateCell();
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [isAlertOpen, setIsAlertOpen] = useState(false);

  const lookupValuesDict = useSelector(
    (state) => state.appConfiguration.lookupValuesDict
  );

  const activeTab = useSelector((state) => state.filesHandler.activeTab);
  console.log("Current active tab:", activeTab);
  const inspectionsGridApi = useSelector(
    (state) => state.filesHandler.inspectionsGridApi
  );

  const currentAssetFiles = useSelector(
    (state) => state.filesHandler.currentAssetFiles
  );

  const latestAssetClickParams = useSelector(
    (state) => state.appConfiguration.latestAssetClickParams
  );

  const [layoutModel, setLayoutModel] = useState(
    Model.fromJson(initialLayoutJson)
  );

  const [openedFiles, setOpenedFiles] = useState([]);
  const selectedFileRef = useRef(null);
  const selectedFilesRef = useRef(selectedFiles);

  // State to manage the queue of actions
  const [actionQueue, setActionQueue] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const [genAIResults, setGenAIResults] = useState(null);
  const [isGenAILoading, setIsGenAILoading] = useState(false);

  useEffect(() => {
    selectedFilesRef.current = selectedFiles;
  }, [selectedFiles]);

  useEffect(() => {
    if (activeTab !== "assetFiles") {
      dispatch(setCurrentGeneralFile([]));
    }
  }, [activeTab, dispatch]);

  // Effect to process actions sequentially
  useEffect(() => {
    if (actionQueue.length > 0 && !isProcessing) {
      setIsProcessing(true);
      const nextAction = actionQueue[0];
      processAction(nextAction).then(() => {
        setActionQueue((prevQueue) => prevQueue.slice(1)); // Remove the processed action
        setIsProcessing(false);
      });
    }
  }, [actionQueue, isProcessing]);

  const enqueueAction = (action) => {
    setActionQueue((prevQueue) => [...prevQueue, action]);
  };

  const processAction = async (action) => {
    console.log("Processing action:", action);

    if (action.type === FlexLayoutActions.DELETE_TAB) {
      const closedTabId = action.data.node;
      console.log("Tab closed:", closedTabId);

      const cleanedTabId = cleanFileId(closedTabId);
      console.log("Cleaned Tab ID:", cleanedTabId);

      // Update openedFiles state
      setOpenedFiles((prevOpenedFiles) =>
        prevOpenedFiles.filter((file) => file.id !== cleanedTabId)
      );

      // Update selectedFiles based on current open tabs
      await syncSelectedFilesWithOpenTabs();
    }

    return action;
  };

  const onAction = (action) => {
    enqueueAction(action); // Add action to the queue
    return action;
  };

  // Function to clean file ID
  const cleanFileId = (id) => {
    return id.replace(/^file-/, "").split("-")[0];
  };

  const syncSelectedFilesWithOpenTabs = async () => {
    // Get the current layout as JSON
    const layoutJson = layoutModel.toJson();
    const openTabIds = new Set();

    // Find all open tabs with file viewers
    layoutJson.layout.children.forEach((tabset) => {
      tabset.children.forEach((tab) => {
        if (tab.component === "fileViewer" && tab.config && tab.config.fileId) {
          openTabIds.add(tab.config.fileId);
        }
      });
    });

    // Filter selectedFiles to only those with open tabs
    const updatedSelectedFiles = selectedFilesRef.current.filter((file) =>
      openTabIds.has(file.id)
    );

    // Update selectedFiles in the store
    dispatch(setSelectedFiles(updatedSelectedFiles));

    // If no file tabs are open, close the modal
    if (openTabIds.size === 0) {
      setIsLinkModalOpen(false);
    }
  };

  const handleLinkButtonClick = async (filePath) => {
    if (!filePath) return;

    if (!selectedRowData) {
      return;
    }

    const currentFiles = selectedRowData.Files
      ? JSON.parse(selectedRowData.Files)
      : [];
    const isFilePresent = isFileLinked(filePath);

    if (isFilePresent) {
      await handleUnlinkFile(filePath, currentFiles);
    } else {
      const isFileInCurrentAssetFiles = currentAssetFiles.some(
        (file) => file === filePath
      );

      if (isFileInCurrentAssetFiles) {
        setIsAlertOpen(true);
      } else {
        setIsLinkModalOpen(true);
        dispatch(setSelectedFiles([{ path: filePath }]));
      }
    }
  };

  const handleAddButtonClick = () => {
    if (selectedFiles.length > 0) {
      const activeFile = selectedFiles[selectedFiles.length - 1];
      if (activeFile) {
        handleFileClick(activeFile.id, activeFile.name, activeFile.path);
      }
    }
  };

  const handleUnlinkFile = async (filePath, currentFiles) => {
    const updatedFiles = currentFiles.filter((file) => file.path !== filePath);

    try {
      const success = await updateCell(
        "Files",
        JSON.stringify(updatedFiles),
        selectedRowData.rowIndex,
        "Previous Inspections"
      );
      if (success) {
        dispatch(
          updateInspectionData({
            rowIndex: selectedRowData.rowIndex,
            Files: JSON.stringify(updatedFiles),
          })
        );
        await logFileAction("unlink", "PI");
        dispatch(removeFileFromInspection(filePath));
      }
    } catch (error) {
      console.error("Error updating cell:", error);
    }
  };

  const handleLinkModalClose = () => {
    setIsLinkModalOpen(false);
    setSelectedCategory(null);
  };

  const handleLinkModalSave = async () => {
    if (selectedFiles.length > 0 && selectedRowData) {
      const category =
        selectedCategory ||
        (lookupValuesDict["File Category"] &&
          lookupValuesDict["File Category"][0]);
      if (category) {
        const currentFiles = selectedRowData.Files
          ? JSON.parse(selectedRowData.Files)
          : [];
        selectedFiles.forEach((selectedFile) => {
          const isFilePresent = currentFiles.some(
            (file) => file.path === selectedFile.path
          );
          if (!isFilePresent) {
            const updatedFiles = [
              ...currentFiles,
              { path: selectedFile.path, category },
            ];

            updateCellAndDispatch(JSON.stringify(updatedFiles));
            logFileAction("link", "PI", category);
            dispatch(
              addFileToInspection({ path: selectedFile.path, category })
            );
          }
        });

        setIsLinkModalOpen(false);
        setSelectedCategory(null);
      }
    }
  };

  const updateCellAndDispatch = async (updatedFiles) => {
    try {
      const success = await updateCell(
        "Files",
        updatedFiles,
        selectedRowData.rowIndex,
        "Previous Inspections"
      );
      if (success) {
        dispatch(
          updateInspectionData({
            rowIndex: selectedRowData.rowIndex,
            Files: updatedFiles,
          })
        );
      }
    } catch (error) {
      console.error("Error updating cell:", error);
    }
  };

  const handleFileClick = async (fileId, filename, path) => {
    if (!fileId || !filename || !path) {
      return;
    }

    const layoutJson = layoutModel.toJson();
    let viewerTabset = layoutJson.layout.children.find(
      (child) => child.id === "viewer-tabset"
    );

    if (!viewerTabset) {
      console.warn("Viewer tabset not found in layout. Recreating it.");

      viewerTabset = {
        type: "tabset",
        id: "viewer-tabset",
        weight: 67,
        selected: 0,
        children: [],
      };

      layoutJson.layout.children.unshift(viewerTabset);
    }

    if (!viewerTabset.children) {
      viewerTabset.children = [];
    }

    const existingTab = viewerTabset.children.find(
      (tab) => tab.config && tab.config.fileId === fileId
    );

    if (existingTab) {
      viewerTabset.selected = viewerTabset.children.indexOf(existingTab);
    } else {
      const mainTab = viewerTabset.children.find(
        (tab) => tab.id === "main-viewer-tab"
      );

      if (mainTab && !mainTab.config) {
        mainTab.name = filename;
        mainTab.config = { fileId, filename, path };
        mainTab.id = `file-${fileId}`; // Update tab ID
        viewerTabset.selected = viewerTabset.children.indexOf(mainTab);
      } else {
        const newTab = {
          type: "tab",
          component: "fileViewer",
          name: filename,
          id: `file-${fileId}-${Math.random().toString(36).substr(2, 9)}`,
          config: { fileId, filename, path },
          enableClose: true,
          enableRename: false,
          tabEnableDrag:false,
          tabEnableFLoat:true
        };

        viewerTabset.children.push(newTab);
        viewerTabset.selected = viewerTabset.children.length - 1;
      }
    }

    setLayoutModel(Model.fromJson(layoutJson));
    dispatch(addSelectedFile({ id: fileId, name: filename, path: path }));
    setOpenedFiles((prev) => [...prev, { id: fileId, filename, path }]);

    selectedFileRef.current = { id: fileId, filename, path };

    await logFileAction("view", "FT");
  };

  const isFileLinked = (filePath) => {
    if (selectedRowData && selectedRowData.Files) {
      const files = JSON.parse(selectedRowData.Files);
      return files.some((file) => file.path === filePath);
    }
    return false;
  };

  const logFileAction = async (action, module, category = "") => {
    try {
      if (!selectedFileRef.current) {
        console.error("No file selected for logging.");
        return;
      }

      const { path } = selectedFileRef.current;

      const payload = {
        action,
        file_path: path,
        submitter: user.email,
        tag_name: latestAssetClickParams["Tag Name"],
        plant: latestAssetClickParams["Plant"],
        unit: latestAssetClickParams["Unit"],
        system: latestAssetClickParams["System"],
        asset_classification: latestAssetClickParams["Asset Classification"],
        equipment_type: latestAssetClickParams["Equipment Type"],
        module: module,
        category: category || null,
      };

      console.log("Logging file action:", payload);

      const response = await axios.post(
        `${process.env.REACT_APP_DATA_API}/file_action_tracker/`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            "session-id": localStorage.getItem("session-id"),
          },
        }
      );

      console.log("File action logged successfully:", response.data);
    } catch (error) {
      console.error("Error logging file action:", error);
    }
  };

  const callGenAI = async (filePath, fileId) => {
    try {
      // Create the tab immediately with loading state
      createGenAIResultsTab(fileId, filePath, true);

      setIsGenAILoading(true);
      const use_case = "inspection_report";

      console.log("Calling GenAI API with fileId:", fileId);

      const response = await axios.get(
        `${process.env.REACT_APP_DATA_API}/run_genai/${fileId}/${use_case}?architecture=default`,

        {
          headers: {
            "Content-Type": "application/json",
            "session-id": localStorage.getItem("session-id"),
          },
        }
        );

      console.log("GenAI response:", response.data);
      setGenAIResults(response.data);

      // Update the tab with results
      createGenAIResultsTab(fileId, filePath, false);

      AppToaster.show({
        message: "AI analysis completed successfully",
        intent: "success",
      });
    } catch (error) {
      console.error("Error calling GenAI API:", error);
      AppToaster.show({
        message: `Error analyzing document: ${error.message}`,
        intent: "danger",
      });

      // Update the tab to show error state
      createGenAIResultsTab(fileId, filePath, false, true);
    } finally {
      setIsGenAILoading(false);
    }
  };

  const createGenAIResultsTab = (fileId, filePath, isLoading = false, hasError = false) => {
    const layoutJson = layoutModel.toJson();
    let filesTabset = layoutJson.layout.children.find(
      (child) => child.id === "files-tabset"
    );

    if (!filesTabset) {
      console.warn("Files tabset not found in layout.");
      return;
    }

    // Check if results tab already exists
    const existingResultsTabIndex = filesTabset.children.findIndex(
      (tab) => tab.component === "aiResults" && tab.config && tab.config.fileId === fileId
    );

    if (existingResultsTabIndex !== -1) {
      // Update the existing tab
      filesTabset.children[existingResultsTabIndex].config = {
        fileId,
        filePath,
        isLoading,
        hasError
      };
      filesTabset.selected = existingResultsTabIndex;
    } else {
      // Create a new tab
      const newTab = {
        type: "tab",
        component: "aiResults",
        name: "AI Results",
        id: `ai-results-${fileId}-${Math.random().toString(36).substr(2, 9)}`,
        config: {
          fileId,
          filePath,
          isLoading,
          hasError
        },
        enableClose: true,
        enableRename: false
      };

      filesTabset.children.push(newTab);
      filesTabset.selected = filesTabset.children.length - 1;
    }

    setLayoutModel(Model.fromJson(layoutJson));
  };

  const handlePageNavigation = (pageNumber) => {
    try {
      // Access the PDF viewer instance directly from the window object
      if (window.pdfViewerInstance && typeof window.pdfViewerInstance.goToPage === 'function') {
        console.log(`Navigating to page ${pageNumber}`);
        // Fix: Pass the exact page number without subtracting 1
        window.pdfViewerInstance.goToPage(pageNumber);
      } else {
        console.log("PDF viewer instance not found or goToPage not available");
      }
    } catch (error) {
      console.error("Error navigating to page:", error);
    }
  };

  const aiLoadingValue = useMemo(() => ({
    isAILoading: isGenAILoading,
    setIsAILoading: setIsGenAILoading
  }), [isGenAILoading]);

  // Detect if file is an image based on extension
  const isImageFile = (filename) => {
    if (!filename) return false;
    const lowercaseFilename = filename.toLowerCase();
    return /\.(jpg|jpeg|png|gif|bmp|webp|svg|tiff|tif)$/i.test(lowercaseFilename);
  };

  // Render file content based on file type
  const renderFileContent = (config) => {
    if (!config || !config.filename || !config.path) {
      return null;
    }

    const isImage = isImageFile(config.filename);

    if (isImage) {
      return (
        <ImageViewer
          fileId={config.fileId}
          filePath={config.path}
          isDarkTheme={isDarkTheme}
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            height: "100%",
            width: "100%"
          }}
        />
      );
    } else {
    // Use MultiPagePDFViewer for PDF files
   return (
    <PDFJsViewer
      filePath={config.path}
      fileId={config.fileId}
      isDarkTheme={isDarkTheme}
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        height: "100%",
        width: "100%"
      }}
    />
  );
  }
};

  // Factory function for creating components
  const factory = (node) => {
    const component = node.getComponent();
    const config = node.getConfig();

    if (component === "mainContent" || component === "fileViewer") {
      return (
        <div style={{ height: "calc(100% - 33px)", width: "100%", overflow: "hidden", display: "flex", flexDirection: "column", position: "relative" }}>
          {config ? (
            <>
              <Card
                style={{
                  height: "32px",
                  padding: "0 10px",
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                  overflow: "hidden",
                  marginBottom: "1px",
                  zIndex: 10, // Ensure card is above other components
                  position: "relative" // Ensure proper stacking context
                }}
              >
                <Tag
                minimal
                  style={{
                    fontWeight: "bold",
                    flexShrink: 1,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {config.path}
                </Tag>
                <Divider type="vertical" />
                {/* Always render the file path */}
                <Button
                  icon="link"
                  intent={
                    selectedRowData && isFileLinked(config.path)
                      ? "warning"
                      : "primary"
                  }
                  onClick={() => handleLinkButtonClick(config.path)}
                  className={`link-button ${
                    selectedRowData && isFileLinked(config.path)
                      ? "my-prime-button-break"
                      : ""
                  }`}
                  disabled={!selectedRowData}
                />
                {ENABLE_AI_FEATURES && (
                  <Button
                    icon={<Bot size={16} />}
                    intent="none"
                    style={{
                      backgroundColor: activeTab === 'previousInspections' && !isGenAILoading && latestAssetClickParams && latestAssetClickParams["Tag Name"] ? "#BD6BBD" : "#5C7080",
                      color: "white",
                      marginLeft: "5px",
                      width: "30px",
                      height: "30px",
                      padding: "0",
                      minWidth: "30px",
                      minHeight: "30px",
                      opacity: activeTab === 'previousInspections' && !isGenAILoading && latestAssetClickParams && latestAssetClickParams["Tag Name"] ? 1 : 0.7
                    }}
                    onClick={() => callGenAI(config.path, config.fileId)}
                    //disabled={activeTab !== 'previousInspections' || isGenAILoading || !latestAssetClickParams || !latestAssetClickParams["Tag Name"]}
                    disable={isGenAILoading}
                    title={isGenAILoading ? "AI analysis in progress..." : "Analyze with AI"}
                  />
                )}
              </Card>
              <div style={{
                height: "calc(100% - 33px)", // Adjusted to account for the Card height
                width: "100%",
                display: "flex",
                flexDirection: "column",
                position: "relative"
              }}>
                {renderFileContent(config)}
              </div>
            </>
          ) : (
            <div className="centered-non-ideal" style={{ height: "100%", display: "flex", alignItems: "center", justifyContent: "center" }}>
              <NonIdealState
                icon="document-open"
                title="No File Selected"
                description="Select a file from the table to be displayed"
              />
            </div>
          )}
        </div>
      );
    } else if (component === "sidebar") {
      return (
        <FilesTable onFileClick={handleFileClick} isDarkTheme={isDarkTheme} />
      );
    } else if (component === "fileExplorer") {
      return (
        <VSCodeTreeExplorer
          onFileClick={handleFileClick}
          isDarkTheme={isDarkTheme}
          openFiles={openedFiles}
        />
      );
    } else if (component === "aiResults") {
      // Only render AI results if the feature is enabled
      if (!ENABLE_AI_FEATURES) {
        return (
          <div className="centered-non-ideal" style={{ height: "100%", display: "flex", alignItems: "center", justifyContent: "center" }}>
            <NonIdealState
              icon="disable"
              title="AI Features Disabled"
              description="AI features are currently disabled in this build. Enable them by setting ENABLE_AI_FEATURES to true."
            />
          </div>
        );
      }

      return (
        <GenAIResultsTable
          results={genAIResults}
          isDarkTheme={isDarkTheme}
          isLoading={config?.isLoading}
          fileId={config?.fileId}
          filePath={config?.filePath}
          hasError={config?.hasError}
          onPageNavigation={handlePageNavigation}
        />
      );
    }

    return null;
  };

  // Update the global reload function with the actual implementation
  useEffect(() => {
    // Function to reload data for the Previous Inspections tab
    window.reloadPreviousInspectionsData = () => {
      console.log("Reloading Previous Inspections data");

      // Find the latest asset click parameters to reload the data
      if (window.latestAssetClickParams) {
        // Use setTimeout to ensure the backend has time to process the changes
        setTimeout(() => {
          try {
            console.log("Reloading with params:", window.latestAssetClickParams);
            // Call the function that originally loaded the data
            if (window.handleFocusAssetClick) {
              window.handleFocusAssetClick(window.latestAssetClickParams);

              AppToaster.show({
                message: "Data refreshed successfully",
                intent: "success",
              });
            }
          } catch (error) {
            console.error("Error reloading data:", error);
            AppToaster.show({
              message: "Error refreshing data",
              intent: "danger",
            });
          }
        }, 1000);
      } else {
        console.warn("No asset click parameters available for reload");
      }
    };

    return () => {
      // Clean up the global function when component unmounts
      window.reloadPreviousInspectionsData = () => {
        console.log("Global reload function called after component unmount");
      };
    };
  }, []);

  return (
    <AILoadingContext.Provider value={aiLoadingValue}>
      <div
        style={{
          position: "relative",
          height: "100%",
          width: "100%",
          overflow: "hidden"
        }}
      >
        {isLoading && (
          <div className="loading-overlay">
            <Spinner />
          </div>
        )}
        <Layout
          model={layoutModel}
          factory={factory}
          onAction={onAction}
          style={{ height: "100%" }}
        />
        <Alert
          className={isDarkTheme ? "bp5-dark" : ""}
          isOpen={isAlertOpen}
          onClose={() => setIsAlertOpen(false)}
        >
          <p>
            File --{" "}
            <b>
              {selectedFiles.map((file) => file.path.split("/").pop()).join(", ")}
            </b>{" "}
            -- is already associated with this asset.
          </p>
        </Alert>

        <Dialog
          className={isDarkTheme ? "bp5-dark" : ""}
          isOpen={isLinkModalOpen}
          onClose={() => {
            handleLinkModalClose();
            setSelectedCategory(null);
          }}
          title="Link File"
          intent={Intent.PRIMARY}
        >
          <div className="bp5-dialog-body">
            <Select
              items={lookupValuesDict["File Category"] || []}
              onItemSelect={setSelectedCategory}
              itemRenderer={(item, { handleClick, modifiers }) => (
                <MenuItem
                  key={item}
                  onClick={handleClick}
                  text={item}
                  active={modifiers.active}
                />
              )}
              activeItem={
                selectedCategory ||
                (lookupValuesDict["File Category"] &&
                  lookupValuesDict["File Category"][0])
              }
            >
              <Button
                text={
                  selectedCategory ||
                  (lookupValuesDict["File Category"] &&
                    lookupValuesDict["File Category"][0]) ||
                  "Select Category"
                }
                rightIcon="caret-down"
              />
            </Select>
          </div>
          <div className="bp5-dialog-footer">
            <div className="bp5-dialog-footer-actions">
              <Button intent="danger" onClick={handleLinkModalClose}>
                Cancel
              </Button>
              <Button intent={Intent.PRIMARY} onClick={handleLinkModalSave}>
                Save
              </Button>
            </div>
          </div>
        </Dialog>
      </div>
    </AILoadingContext.Provider>
  );
}

export default FilesHandler;