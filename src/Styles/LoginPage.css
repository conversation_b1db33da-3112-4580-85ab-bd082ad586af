/* VSCode-inspired Login Page Styles */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1C2127 0%, #2F343C 50%, #252A31 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(45, 114, 210, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(251, 179, 96, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(1, 137, 119, 0.06) 0%, transparent 50%);
  pointer-events: none;
}

.login-card {
  width: 420px;
  background: rgba(47, 52, 60, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(56, 62, 71, 0.8);
  border-radius: 12px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(45, 114, 210, 0.5), transparent);
}

.login-header {
  padding: 40px 40px 20px;
  text-align: center;
  position: relative;
}

.login-logo {
  width: 80px;
  height: auto;
  margin-bottom: 20px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.login-logo:hover {
  transform: scale(1.05);
}

.login-title {
  color: #F5F8FA !important;
  font-size: 28px !important;
  font-weight: 700 !important;
  margin: 0 0 8px !important;
  background: linear-gradient(135deg, #F5F8FA 0%, #CED9E0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  color: #A7B6C2 !important;
  font-size: 16px !important;
  margin: 0 !important;
  opacity: 0.9;
}

.login-form-container {
  padding: 20px 40px 40px;
}

.login-form-item {
  margin-bottom: 24px !important;
}

.login-form-item:last-child {
  margin-bottom: 0 !important;
}

/* Plant Selector Styling */
.plant-selector-container {
  position: relative;
}

.plant-selector-container .ant-select {
  width: 100% !important;
  height: 48px !important;
}

.plant-selector-container .ant-select .ant-select-selector {
  background: rgba(28, 33, 39, 0.8) !important;
  border: 1px solid rgba(56, 62, 71, 0.6) !important;
  border-radius: 8px !important;
  height: 48px !important;
  padding: 0 16px !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px);
}

.plant-selector-container .ant-select .ant-select-selector:hover {
  border-color: rgba(45, 114, 210, 0.6) !important;
  background: rgba(28, 33, 39, 0.9) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(45, 114, 210, 0.15);
}

.plant-selector-container .ant-select-focused .ant-select-selector {
  border-color: #2D72D2 !important;
  box-shadow: 0 0 0 2px rgba(45, 114, 210, 0.2) !important;
  background: rgba(28, 33, 39, 1) !important;
}

.plant-selector-container .ant-select-selection-placeholder {
  color: #A7B6C2 !important;
  font-size: 14px !important;
  line-height: 46px !important;
}

.plant-selector-container .ant-select-selection-item {
  color: #F5F8FA !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  line-height: 46px !important;
}

/* Google Sign-In Button Container */
.google-signin-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 48px;
  border-radius: 8px;
  background: rgba(28, 33, 39, 0.6);
  border: 1px solid rgba(56, 62, 71, 0.6);
  transition: all 0.3s ease;
  overflow: hidden;
}

.google-signin-container:hover {
  background: rgba(28, 33, 39, 0.8);
  border-color: rgba(45, 114, 210, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.google-signin-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(45, 114, 210, 0.1), transparent);
  transition: left 0.6s ease;
}

.google-signin-container:hover::before {
  left: 100%;
}

/* Alert Styling */
.login-alert {
  background: rgba(251, 179, 96, 0.1) !important;
  border: 1px solid rgba(251, 179, 96, 0.3) !important;
  border-radius: 8px !important;
  margin-bottom: 20px !important;
}

.login-alert .ant-alert-message {
  color: #FBB360 !important;
  font-weight: 500 !important;
}

/* Loading States */
.login-loading {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

.login-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid #2D72D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-card {
    width: 100%;
    max-width: 380px;
    margin: 0 20px;
  }
  
  .login-header {
    padding: 30px 30px 15px;
  }
  
  .login-form-container {
    padding: 15px 30px 30px;
  }
  
  .login-title {
    font-size: 24px !important;
  }
  
  .login-subtitle {
    font-size: 14px !important;
  }
}

/* Subtle animations */
.login-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for accessibility */
.plant-selector-container .ant-select:focus-within {
  outline: 2px solid rgba(45, 114, 210, 0.5);
  outline-offset: 2px;
}

.google-signin-container:focus-within {
  outline: 2px solid rgba(45, 114, 210, 0.5);
  outline-offset: 2px;
}
