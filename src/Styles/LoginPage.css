/* VSCode-inspired Login Page Styles */
.login-container {
  min-height: 100vh;
  background: #1C2127;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.login-card {
  width: 420px;
  background: #2F343C;
  border: 1px solid #383E47;
  border-radius: 0;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  position: relative;
  z-index: 1;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  padding: 40px 40px 20px;
  text-align: center;
  position: relative;
}

.login-logo {
  width: 80px;
  height: auto;
  margin-bottom: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease, filter 0.3s ease;
  cursor: pointer;
}

.login-logo:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 4px 8px rgba(45, 114, 210, 0.3));
}

.login-title {
  color: #F5F8FA !important;
  font-size: 28px !important;
  font-weight: 700 !important;
  margin: 0 0 8px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}

.login-subtitle {
  color: #A7B6C2 !important;
  font-size: 16px !important;
  margin: 0 !important;
  opacity: 0.9;
}

.login-form-container {
  padding: 20px 40px 40px;
}

.login-form-item {
  margin-bottom: 24px !important;
}

.login-form-item:last-child {
  margin-bottom: 0 !important;
}

/* Plant Selector Styling */
.plant-selector-container {
  position: relative;
}

.plant-selector-container .ant-select {
  width: 100% !important;
  height: 56px !important;
}

.plant-selector-container .ant-select .ant-select-selector {
  background: #1C2127 !important;
  border: 1px solid #383E47 !important;
  border-radius: 0 !important;
  height: 56px !important;
  padding: 0 16px !important;
  transition: all 0.2s ease !important;
}

.plant-selector-container .ant-select .ant-select-selector:hover {
  border-color: #2D72D2 !important;
  background: #252A31 !important;
}

.plant-selector-container .ant-select-focused .ant-select-selector {
  border-color: #2D72D2 !important;
  box-shadow: 0 0 0 1px #2D72D2 !important;
  background: #1C2127 !important;
}

.plant-selector-container .ant-select-selection-placeholder {
  color: #A7B6C2 !important;
  font-size: 15px !important;
  line-height: 54px !important;
}

.plant-selector-container .ant-select-selection-item {
  color: #F5F8FA !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  line-height: 54px !important;
}

/* Google Sign-In Button Container */
.google-signin-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 56px;
  border-radius: 0;
  background: #1C2127;
  border: 1px solid #383E47;
  transition: all 0.3s ease;
  overflow: hidden;
}

.google-signin-container:hover {
  background: #252A31;
  border-color: #2D72D2;
  box-shadow: 0 2px 8px rgba(45, 114, 210, 0.2);
}

.google-signin-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(45, 114, 210, 0.1), transparent);
  transition: left 0.5s ease;
}

.google-signin-container:hover::before {
  left: 100%;
}

/* Alert Styling */
.login-alert {
  background: rgba(251, 179, 96, 0.1) !important;
  border: 1px solid rgba(251, 179, 96, 0.3) !important;
  border-radius: 0 !important;
  margin-bottom: 20px !important;
}

.login-alert .ant-alert-message {
  color: #FBB360 !important;
  font-weight: 500 !important;
}

/* Loading States */
.login-loading {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

.login-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid #2D72D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-card {
    width: 100%;
    max-width: 380px;
    margin: 0 20px;
  }
  
  .login-header {
    padding: 30px 30px 15px;
  }
  
  .login-form-container {
    padding: 15px 30px 30px;
  }
  
  .login-title {
    font-size: 24px !important;
  }
  
  .login-subtitle {
    font-size: 14px !important;
  }
}



/* Focus states for accessibility */
.plant-selector-container .ant-select:focus-within {
  outline: 1px solid #2D72D2;
  outline-offset: 1px;
}

.google-signin-container:focus-within {
  outline: 1px solid #2D72D2;
  outline-offset: 1px;
}
