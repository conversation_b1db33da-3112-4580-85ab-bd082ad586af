/* VSCode Tree Explorer Styles */
.vscode-tree-explorer {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.tree-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  position: relative;
}

.tree-card.dark {
  background-color: #1C2127;
  border-color: #2F343C;
}

.tree-card.light {
  background-color: #ffffff;
  border-color: #e1e8ed;
}

.controls-container {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.tree-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.01) 0%, transparent 100%);
  border-radius: 6px;
  padding: 4px;
}

.tree-content {
  height: 100%;
  overflow: auto;
  flex: 1;
}

.tree-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  height: 28px;
  line-height: 28px;
  border: 1px solid transparent;
  border-radius: 4px;
  margin: 1px 4px;
  transition: all 0.15s ease;
  position: relative;
}

.tree-item:hover {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateX(2px);
}

.tree-item.dark:hover {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.1);
}

.tree-item.light:hover {
  background-color: rgba(0, 0, 0, 0.08);
  border-color: rgba(0, 0, 0, 0.1);
}

.tree-item.folder {
  font-weight: 600;
  background: linear-gradient(90deg, rgba(251, 179, 96, 0.05) 0%, transparent 100%);
}

.tree-item.file {
  font-weight: 500;
  padding-left: 8px;
}

.tree-item.file-open {
  background: linear-gradient(90deg, rgba(45, 114, 210, 0.15) 0%, rgba(45, 114, 210, 0.05) 100%);
  border-color: #2D72D2;
  border-left: 3px solid #2D72D2;
  font-weight: 600;
}

.tree-item.file-open.light {
  background: linear-gradient(90deg, rgba(45, 114, 210, 0.1) 0%, rgba(45, 114, 210, 0.03) 100%);
  border-color: #2D72D2;
  border-left: 3px solid #2D72D2;
}

.tree-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
}

.expand-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.expand-icon-placeholder {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.folder-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-status-indicator {
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.item-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  color: inherit;
}

.folder-count-tag {
  flex-shrink: 0;
  margin-left: auto;
  background: #2D72D2;
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 11px;
  min-width: 20px;
  text-align: center;
}



.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  gap: 16px;
  position: absolute;
  top: 0;
  left: 0;
}

/* Dark theme specific styles */
.bp5-dark .tree-item {
  color: #F5F8FA;
}

.bp5-dark .tree-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.bp5-dark .tree-item.file-open {
  background-color: #252A31;
}

/* Light theme specific styles */
.bp5-light .tree-item {
  color: #182026;
}

.bp5-light .tree-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.bp5-light .tree-item.file-open {
  background-color: #f5f8fa;
}

/* Scrollbar styling */
.tree-content::-webkit-scrollbar {
  width: 8px;
}

.tree-content::-webkit-scrollbar-track {
  background: transparent;
}

.tree-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.tree-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Ant Design dropdown menu styling for context menus */
.files-table-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Dark theme specific styles */
.bp5-dark .tree-item {
  color: #F5F8FA;
}

.bp5-dark .tree-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.bp5-dark .tree-item.file-open {
  background-color: #252A31;
}

/* Light theme specific styles */
.bp5-light .tree-item {
  color: #182026;
}

.bp5-light .tree-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.bp5-light .tree-item.file-open {
  background-color: #f5f8fa;
}

/* Ensure proper spacing for virtualized items */
.tree-item.expanded {
  /* Additional styles for expanded folders if needed */
}

/* Animation for expand/collapse */
.expand-icon svg {
  transition: transform 0.1s ease;
}

.tree-item.expanded .expand-icon svg {
  transform: rotate(90deg);
}
