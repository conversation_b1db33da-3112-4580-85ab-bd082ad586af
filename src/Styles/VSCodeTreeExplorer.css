/* VSCode Tree Explorer Styles */
.vscode-tree-explorer {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.tree-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.tree-card.dark {
  background-color: #1C2127;
  border-color: #2F343C;
}

.tree-card.light {
  background-color: #ffffff;
  border-color: #e1e8ed;
}

.controls-container {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.tree-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tree-content {
  height: 100%;
  overflow: auto;
  flex: 1;
}

.tree-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  height: 24px;
  line-height: 24px;
  border: 1px solid transparent;
  transition: background-color 0.1s ease;
}

.tree-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.tree-item.dark:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.tree-item.light:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.tree-item.folder {
  font-weight: 500;
}

.tree-item.file {
  font-weight: 400;
}

.tree-item.file-open {
  background-color: #1C2127;
  border-color: #2D72D2;
}

.tree-item.file-open.light {
  background-color: #f5f8fa;
  border-color: #2D72D2;
}

.tree-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 4px;
  padding: 0 4px;
}

.expand-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.expand-icon-placeholder {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.folder-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-status-indicator {
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.item-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  color: inherit;
}

.folder-count-tag {
  flex-shrink: 0;
  margin-left: auto;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
}

/* Dark theme specific styles */
.bp5-dark .tree-item {
  color: #F5F8FA;
}

.bp5-dark .tree-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.bp5-dark .tree-item.file-open {
  background-color: #252A31;
}

/* Light theme specific styles */
.bp5-light .tree-item {
  color: #182026;
}

.bp5-light .tree-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.bp5-light .tree-item.file-open {
  background-color: #f5f8fa;
}

/* Scrollbar styling */
.tree-content::-webkit-scrollbar {
  width: 8px;
}

.tree-content::-webkit-scrollbar-track {
  background: transparent;
}

.tree-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.tree-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Ant Design dropdown menu styling for context menus */
.files-table-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Ensure proper spacing for virtualized items */
.tree-item.expanded {
  /* Additional styles for expanded folders if needed */
}

/* Animation for expand/collapse */
.expand-icon svg {
  transition: transform 0.1s ease;
}

.tree-item.expanded .expand-icon svg {
  transform: rotate(90deg);
}
