import React, { useContext, useState } from 'react';
import { ConfigProvider, theme } from 'antd';
import { AppContext } from '../AppContextProvider';

const darkTheme = {
  algorithm: theme.darkAlgorithm,
  token: {
    borderRadius: 0,
    colorPrimary: "#4C90F0",
    colorBgElevated: "#252A31",
    colorText: "#FFFFFF",
    colorTextPlaceholder: "#d3d3d3",
  },
  components: {
    Select: {
      optionActiveBg: '#5F6B7C',
      optionSelectedBg: '#2D72D2',
      multipleItemBg: '#2D72D2',
    },
    Button: {
      colorBgContainer: '#252A31',
      colorBorder: '#252A31',
      colorBgContainerDisabled: '#252A31',
      colorText: '#FFFFFF',
      colorTextDisabled: '#8A9BA8',
      colorBgTextHover: '#404854',
      colorBgTextActive: '#404854',
      colorBorderSecondary: '#252A31',
      colorBgContainerSecondary: '#252A31',
    },
    Carousel: {
      arrowOffset: -10,
      arrowSize: 20,

    },
  },
};

const lightTheme = {
  algorithm: theme.defaultAlgorithm,
  token: {
    borderRadius: 0,
  },
  components: {
    
    Carousel: {
      arrowOffset: -10,
      arrowSize: 200,
      
    },
  }
};

const AntDesignConfig = ({ children }) => {
  const { isDarkTheme } = useContext(AppContext);

  return (
    <ConfigProvider theme={isDarkTheme ? darkTheme : lightTheme}>
      {children}
    </ConfigProvider>
  );
};

export default AntDesignConfig;
